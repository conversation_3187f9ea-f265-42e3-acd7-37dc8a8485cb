"""
Comprehensive Payment Service for Subscription Management
"""
import logging
import uuid
from decimal import Decimal
from django.conf import settings
from django.utils import timezone
from django.db import transaction
from dateutil.relativedelta import relativedelta
import razorpay

from .models import Package, Subscription, Coupon, GiftCard, Subscription_Invoice, FailedPaymentLog
from students.models import Student
from .notifications import SubscriptionNotificationService

logger = logging.getLogger(__name__)


class PaymentService:
    """Service class to handle all payment-related operations"""
    
    def __init__(self):
        self.client = self._initialize_razorpay_client()
    
    def _initialize_razorpay_client(self):
        """Initialize Razorpay client with proper error handling"""
        try:
            if not settings.RAZORPAY_KEY_ID or not settings.RAZORPAY_KEY_SECRET:
                logger.error("Razorpay credentials not configured")
                return None
            
            client = razorpay.Client(auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET))
            logger.info(f"Razorpay client initialized with key: {settings.RAZORPAY_KEY_ID[:10]}...")
            return client
        except Exception as e:
            logger.error(f"Failed to initialize Razorpay client: {str(e)}")
            return None
    
    def calculate_final_price(self, package, coupon_code=None, gift_card_code=None, gift_card_pin=None, student=None):
        """Calculate final price after applying coupons and gift cards"""
        final_price = package.discount_price or package.price
        applied_coupon = None
        applied_gift_card = None
        gift_card_amount_used = 0
        
        logger.info(f"Calculating price for package {package.id}: base price ₹{final_price}")
        
        # Apply coupon
        if coupon_code:
            try:
                coupon = Coupon.objects.get(code=coupon_code)
                
                # Check if coupon is already used by this student
                if student and Subscription.objects.filter(student=student, coupon_used=coupon_code).exists():
                    raise Exception("Coupon already used by this student")
                
                if not coupon.is_valid():
                    raise Exception("Coupon is invalid or expired")
                
                original_price = final_price
                if coupon.discount_type == "percentage":
                    final_price -= final_price * (coupon.discount / 100)
                else:
                    final_price -= coupon.discount
                
                final_price = max(0, final_price)  # Ensure price doesn't go negative
                applied_coupon = coupon
                
                logger.info(f"Coupon {coupon_code} applied: ₹{original_price} -> ₹{final_price}")
                
            except Coupon.DoesNotExist:
                raise Exception("Invalid coupon code")
            except Exception as e:
                logger.error(f"Coupon application failed: {str(e)}")
                raise
        
        # Apply gift card
        if gift_card_code and gift_card_pin:
            try:
                gift_card = GiftCard.objects.get(code=gift_card_code)
                
                if not gift_card.is_valid_combination(gift_card_code, gift_card_pin, gift_card.unique_id):
                    raise Exception("Invalid gift card or PIN")
                
                if gift_card.balance <= 0:
                    raise Exception("Gift card has no balance")
                
                original_price = final_price
                if gift_card.balance >= final_price:
                    gift_card_amount_used = final_price
                    final_price = 0
                else:
                    gift_card_amount_used = gift_card.balance
                    final_price -= gift_card.balance
                
                applied_gift_card = gift_card
                
                logger.info(f"Gift card {gift_card_code} applied: ₹{gift_card_amount_used} used, final price: ₹{final_price}")
                
            except GiftCard.DoesNotExist:
                raise Exception("Invalid gift card code")
            except Exception as e:
                logger.error(f"Gift card application failed: {str(e)}")
                raise
        
        return {
            'final_price': final_price,
            'applied_coupon': applied_coupon,
            'applied_gift_card': applied_gift_card,
            'gift_card_amount_used': gift_card_amount_used
        }
    
    def create_razorpay_order(self, amount, student, package):
        """Create Razorpay order with proper error handling"""
        if amount <= 0:
            logger.info("Amount is 0, skipping Razorpay order creation")
            return None

        # Check if we're in test mode and use mock order
        if getattr(settings, 'RAZORPAY_TEST_MODE', False) and not self.client:
            logger.info("Using mock Razorpay order for testing")
            mock_order = {
                'id': f'order_mock_{student.id}_{package.id}_{int(amount * 100)}',
                'amount': int(amount * 100),
                'currency': 'INR',
                'status': 'created'
            }
            logger.info(f"Mock Razorpay order created: {mock_order['id']} for ₹{amount}")
            return mock_order

        if not self.client:
            raise Exception("Payment gateway not available")

        try:
            order_data = {
                "amount": int(amount * 100),  # Convert to paise
                "currency": "INR",
                "payment_capture": 1,
                "notes": {
                    "student_id": student.id,
                    "student_name": f"{student.user.first_name} {student.user.last_name}",
                    "package_id": package.id,
                    "package_name": package.name,
                    "package_type": package.package_type,
                }
            }

            order = self.client.order.create(order_data)
            logger.info(f"Razorpay order created: {order['id']} for ₹{amount}")
            return order

        except Exception as e:
            logger.error(f"Razorpay order creation failed: {str(e)}")
            raise Exception(f"Payment order creation failed: {str(e)}")
    
    @transaction.atomic
    def create_subscription(self, student, package, pricing_info, razorpay_order=None):
        """Create subscription with proper transaction handling"""
        try:
            # Calculate subscription end date
            start_date = timezone.now().date()
            
            if package.package_type == 'validity' and package.duration_months:
                end_date = start_date + relativedelta(months=package.duration_months)
            elif package.package_type == 'event' and package.event_end_date:
                end_date = package.event_end_date.date()
            else:
                end_date = start_date + relativedelta(months=1)  # Default fallback
            
            # Create subscription
            subscription = Subscription.objects.create(
                student=student,
                package=package,
                start_date=start_date,
                end_date=end_date,
                expiry_date=end_date,  # For backward compatibility
                final_price=pricing_info['final_price'],
                pg_order_id=razorpay_order['id'] if razorpay_order else "FREE_OR_GIFT_CARD",
                coupon_used=pricing_info['applied_coupon'].code if pricing_info['applied_coupon'] else None,
                gift_card_used=pricing_info['applied_gift_card'].code if pricing_info['applied_gift_card'] else None,
                transaction_id=str(uuid.uuid4())[:12],
                is_active=(pricing_info['final_price'] == 0)  # Activate immediately if free
            )
            
            # Apply coupon usage
            if pricing_info['applied_coupon']:
                coupon = pricing_info['applied_coupon']
                coupon.usage_limit -= 1
                coupon.save()
                logger.info(f"Coupon {coupon.code} usage decremented")
            
            # Apply gift card usage
            if pricing_info['applied_gift_card']:
                gift_card = pricing_info['applied_gift_card']
                gift_card.balance -= pricing_info['gift_card_amount_used']
                if gift_card.balance <= 0:
                    gift_card.used = True
                gift_card.redeem_at = timezone.now()
                gift_card.save()
                logger.info(f"Gift card {gift_card.code} balance updated: ₹{gift_card.balance}")
            
            # Create invoice
            invoice = Subscription_Invoice.objects.create(
                subscription=subscription,
                student=student,
                amount=package.discount_price or package.price,
                total_amount=pricing_info['final_price'],
                is_paid=(pricing_info['final_price'] == 0)
            )
            
            logger.info(f"Subscription {subscription.id} created for student {student.id}")
            
            # Send notifications
            SubscriptionNotificationService.send_subscription_confirmation(subscription)
            
            # If subscription is free, send activation notification immediately
            if subscription.is_active:
                SubscriptionNotificationService.send_subscription_activation(subscription)
                logger.info(f"Free subscription {subscription.id} activated immediately")
            
            return subscription, invoice
            
        except Exception as e:
            logger.error(f"Subscription creation failed: {str(e)}")
            raise
    
    def verify_payment_signature(self, order_id, payment_id, signature):
        """Verify Razorpay payment signature"""
        # Handle mock payments in test mode
        if getattr(settings, 'RAZORPAY_TEST_MODE', False) and order_id.startswith('order_mock_'):
            logger.info(f"Mock payment verification for order {order_id}")
            if payment_id.startswith('pay_mock_') and signature == 'mock_signature':
                logger.info(f"Mock payment signature verified for order {order_id}")
                return True
            else:
                logger.error(f"Invalid mock payment signature for order {order_id}")
                raise Exception("Invalid mock payment signature")

        if not self.client:
            raise Exception("Payment gateway not available")

        try:
            self.client.utility.verify_payment_signature({
                'razorpay_order_id': order_id,
                'razorpay_payment_id': payment_id,
                'razorpay_signature': signature
            })
            logger.info(f"Payment signature verified for order {order_id}")
            return True
        except razorpay.errors.SignatureVerificationError as e:
            logger.error(f"Invalid payment signature for order {order_id}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Payment verification error for order {order_id}: {str(e)}")
            raise
    
    @transaction.atomic
    def activate_subscription(self, subscription, payment_id, signature):
        """Activate subscription after successful payment"""
        try:
            subscription.pg_payment_id = payment_id
            subscription.pg_signature = signature
            subscription.is_active = True
            subscription.save()
            
            # Mark invoice as paid
            invoice = Subscription_Invoice.objects.get(subscription=subscription)
            invoice.is_paid = True
            invoice.save()
            
            logger.info(f"Subscription {subscription.id} activated successfully")
            
            # Send activation notifications
            SubscriptionNotificationService.send_subscription_activation(subscription)
            
            return True
            
        except Exception as e:
            logger.error(f"Subscription activation failed: {str(e)}")
            raise
    
    def log_failed_payment(self, student, order_id, payment_id, reason):
        """Log failed payment for debugging"""
        try:
            FailedPaymentLog.objects.create(
                user=student,
                order_id=order_id,
                payment_id=payment_id,
                reason=reason
            )
            logger.warning(f"Failed payment logged for student {student.id}: {reason}")
        except Exception as e:
            logger.error(f"Failed to log payment failure: {str(e)}")
