#!/usr/bin/env python3
"""
Test script to verify API endpoints and attachment functionality.
"""

import requests
import json
import os
from datetime import datetime

# Configuration
BASE_URL = "https://api.shashtrarth.com"
TEST_RESULTS_FILE = "api_test_results.json"

class APITester:
    def __init__(self):
        self.base_url = BASE_URL
        self.access_token = None
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "tests": []
        }
    
    def log_test(self, test_name, status, details=None, response_data=None):
        """Log test results."""
        test_result = {
            "test_name": test_name,
            "status": status,
            "details": details,
            "response_data": response_data,
            "timestamp": datetime.now().isoformat()
        }
        self.results["tests"].append(test_result)
        
        status_emoji = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_emoji} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
    
    def test_endpoint_exists(self, endpoint, method="GET", headers=None):
        """Test if an endpoint exists (doesn't return 404)."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            elif method == "POST":
                response = requests.post(url, headers=headers, timeout=10)
            
            if response.status_code == 404:
                return False, f"Endpoint not found (404)"
            else:
                return True, f"Endpoint exists (status: {response.status_code})"
                
        except requests.exceptions.RequestException as e:
            return False, f"Request failed: {str(e)}"
    
    def test_authentication_required(self, endpoint):
        """Test if endpoint properly requires authentication."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 401 or "Authentication required" in response.text:
                return True, "Authentication properly required"
            else:
                return False, f"Authentication not required (status: {response.status_code})"
                
        except requests.exceptions.RequestException as e:
            return False, f"Request failed: {str(e)}"
    
    def test_response_structure(self, endpoint, expected_keys, headers=None):
        """Test if response has expected structure."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    missing_keys = [key for key in expected_keys if key not in data]
                    
                    if not missing_keys:
                        return True, "Response structure is correct"
                    else:
                        return False, f"Missing keys: {missing_keys}"
                        
                except json.JSONDecodeError:
                    return False, "Response is not valid JSON"
            else:
                return False, f"Request failed with status: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            return False, f"Request failed: {str(e)}"
    
    def run_tests(self):
        """Run all tests."""
        print("🚀 Starting API endpoint tests...\n")
        
        # Test 1: Check if new questions endpoint exists
        exists, details = self.test_endpoint_exists("/api/customrcare/questions/")
        self.log_test(
            "New Questions Endpoint Exists",
            "PASS" if exists else "FAIL",
            details
        )
        
        # Test 2: Check if search endpoint exists
        exists, details = self.test_endpoint_exists("/api/customrcare/questions/search/")
        self.log_test(
            "Search Endpoint Exists",
            "PASS" if exists else "FAIL",
            details
        )
        
        # Test 3: Check authentication requirement for questions endpoint
        auth_required, details = self.test_authentication_required("/api/customrcare/questions/")
        self.log_test(
            "Questions Endpoint Authentication",
            "PASS" if auth_required else "FAIL",
            details
        )
        
        # Test 4: Check authentication requirement for search endpoint
        auth_required, details = self.test_authentication_required("/api/customrcare/questions/search/")
        self.log_test(
            "Search Endpoint Authentication",
            "PASS" if auth_required else "FAIL",
            details
        )
        
        # Test 5: Test status update endpoint exists
        exists, details = self.test_endpoint_exists("/api/customrcare/questions/status-update/", method="POST")
        self.log_test(
            "Status Update Endpoint Exists",
            "PASS" if exists else "FAIL",
            details
        )
        
        # Test 6: Check other customer care endpoints
        endpoints_to_test = [
            "/api/customrcare/login/",
            "/api/customrcare/dashboard-api/",
            "/api/customrcare/tickets/",
        ]
        
        for endpoint in endpoints_to_test:
            exists, details = self.test_endpoint_exists(endpoint)
            self.log_test(
                f"Endpoint {endpoint} Exists",
                "PASS" if exists else "FAIL",
                details
            )
        
        # Test 7: Check questions API endpoints (for attachment testing)
        question_endpoints = [
            "/api/questions/",
            "/api/master-questions/",
            "/api/master-options/",
        ]
        
        for endpoint in question_endpoints:
            exists, details = self.test_endpoint_exists(endpoint)
            self.log_test(
                f"Questions API {endpoint} Exists",
                "PASS" if exists else "FAIL",
                details
            )
        
        # Save results to file
        with open(TEST_RESULTS_FILE, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📊 Test results saved to {TEST_RESULTS_FILE}")
        
        # Summary
        total_tests = len(self.results["tests"])
        passed_tests = len([t for t in self.results["tests"] if t["status"] == "PASS"])
        failed_tests = len([t for t in self.results["tests"] if t["status"] == "FAIL"])
        
        print(f"\n📈 Test Summary:")
        print(f"   Total tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed tests:")
            for test in self.results["tests"]:
                if test["status"] == "FAIL":
                    print(f"   - {test['test_name']}: {test['details']}")

def main():
    """Main function to run tests."""
    tester = APITester()
    tester.run_tests()

if __name__ == "__main__":
    main()
