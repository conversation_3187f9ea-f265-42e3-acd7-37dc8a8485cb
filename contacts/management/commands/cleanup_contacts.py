from django.core.management.base import BaseCommand
from django.utils import timezone
from contacts.models import Contact
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up unmatched contacts older than specified hours'

    def add_arguments(self, parser):
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Delete unmatched contacts older than this many hours (default: 24)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about contacts being deleted',
        )

    def handle(self, *args, **options):
        hours = options['hours']
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Starting contact cleanup - removing unmatched contacts older than {hours} hours '
                f'{"(DRY RUN)" if dry_run else ""}'
            )
        )
        
        # Calculate cutoff time
        cutoff_time = timezone.now() - timezone.timedelta(hours=hours)
        
        # Find unmatched contacts older than cutoff
        unmatched_contacts = Contact.objects.filter(
            is_matched=False,
            synced_at__lt=cutoff_time
        ).select_related('user')
        
        total_count = unmatched_contacts.count()
        
        if total_count == 0:
            self.stdout.write(
                self.style.SUCCESS('No unmatched contacts found to clean up.')
            )
            return
        
        self.stdout.write(f'Found {total_count} unmatched contacts to clean up')
        
        if verbose:
            self.stdout.write('\nContacts to be deleted:')
            for contact in unmatched_contacts[:20]:  # Show first 20
                age_hours = (timezone.now() - contact.synced_at).total_seconds() / 3600
                self.stdout.write(
                    f'  - {contact.user.username}: {contact.name} ({contact.contact_number}) '
                    f'- {age_hours:.1f} hours old'
                )
            
            if total_count > 20:
                self.stdout.write(f'  ... and {total_count - 20} more contacts')
        
        if not dry_run:
            # Perform the cleanup
            deleted_count = Contact.cleanup_unmatched_contacts(hours)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully deleted {deleted_count} unmatched contacts'
                )
            )
            
            # Log the cleanup
            logger.info(f'Contact cleanup completed. Deleted {deleted_count} unmatched contacts older than {hours} hours.')
            
            # Show remaining statistics
            remaining_total = Contact.objects.count()
            remaining_matched = Contact.objects.filter(is_matched=True).count()
            remaining_unmatched = Contact.objects.filter(is_matched=False).count()
            
            self.stdout.write(
                f'\nRemaining contacts: {remaining_total} total '
                f'({remaining_matched} matched, {remaining_unmatched} unmatched)'
            )
            
        else:
            self.stdout.write(
                self.style.WARNING(
                    f'DRY RUN: Would delete {total_count} unmatched contacts'
                )
            )
        
        # Show some statistics
        if verbose or dry_run:
            self.stdout.write('\nContact Statistics:')
            total_contacts = Contact.objects.count()
            matched_contacts = Contact.objects.filter(is_matched=True).count()
            unmatched_contacts = Contact.objects.filter(is_matched=False).count()
            
            self.stdout.write(f'  Total contacts: {total_contacts}')
            self.stdout.write(f'  Matched contacts: {matched_contacts}')
            self.stdout.write(f'  Unmatched contacts: {unmatched_contacts}')
            
            # Age distribution of unmatched contacts
            now = timezone.now()
            recent_unmatched = Contact.objects.filter(
                is_matched=False,
                synced_at__gte=now - timezone.timedelta(hours=1)
            ).count()
            old_unmatched = Contact.objects.filter(
                is_matched=False,
                synced_at__lt=now - timezone.timedelta(hours=24)
            ).count()
            
            self.stdout.write(f'  Unmatched contacts < 1 hour old: {recent_unmatched}')
            self.stdout.write(f'  Unmatched contacts > 24 hours old: {old_unmatched}')
        
        self.stdout.write(
            self.style.SUCCESS('Contact cleanup completed!')
        )
