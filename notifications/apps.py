from django.apps import AppConfig
import firebase_admin
from firebase_admin import credentials


class NotificationConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "notifications"

    def ready(self):
        # Initialize Firebase Admin SDK
        try:
            # Replace with your path to Firebase credentials JSON
            cred = credentials.Certificate("serviceAccountKey.json")
            firebase_admin.initialize_app(cred)
        except Exception as e:
            print(f"Firebase initialization error: {e}")
