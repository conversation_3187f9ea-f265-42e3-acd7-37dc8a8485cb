from rest_framework import serializers
from .models import Package, Subscription, Coupon, GiftCard, Subscription_Invoice
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from students.models import Student
from students.serializers import StudentSerializer


class PackageSerializer(serializers.ModelSerializer):
    is_event_active = serializers.ReadOnlyField()

    class Meta:
        model = Package
        fields = '__all__'

    def validate(self, data):
        """Validate package data based on package type"""
        package_type = data.get('package_type')

        if package_type == 'validity':
            if not data.get('duration_months'):
                raise serializers.ValidationError("Validity packages must have a duration in months.")

        elif package_type == 'event':
            if not data.get('event_name'):
                raise serializers.ValidationError("Event packages must have an event name.")

            event_start = data.get('event_start_date')
            event_end = data.get('event_end_date')

            if event_start and event_end and event_start >= event_end:
                raise serializers.ValidationError("Event start date must be before end date.")

        return data


class SubscriptionSerializer(serializers.ModelSerializer):
    student = serializers.PrimaryKeyRelatedField(
        queryset=Student.objects.all(), write_only=True
    )
    package = serializers.PrimaryKeyRelatedField(
        queryset=Package.objects.all(), write_only=True
    )
    student_details = StudentSerializer(source="student", read_only=True)
    package_details = PackageSerializer(source="package", read_only=True)
    coupon_code = serializers.CharField(write_only=True, required=False)
    is_valid_subscription = serializers.ReadOnlyField()

    class Meta:
        model = Subscription
        fields = [
            "student",
            "student_details",
            "package",
            "package_details",
            "id",
            "start_date",
            "end_date",
            'coupon_code',
            "referror",
            "is_active",
            "is_valid_subscription",
        ]
        extra_kwargs = {"end_date": {"required": False}}

    def create(self, validated_data):
        student = validated_data.get("student")
        package = validated_data.get("package")
        start_date = validated_data.get("start_date")

        # Remove coupon_code from validated_data as it's not a model field
        validated_data.pop('coupon_code', None)

        # Let the model's save method handle end_date calculation
        subscription, created = Subscription.objects.update_or_create(
            student=student,
            package=package,
            defaults=validated_data,
        )

        return subscription

    def validate_package(self, value):
        """Validate package selection based on type and availability"""
        if not value.is_active:
            raise serializers.ValidationError("This package is not currently available.")

        if value.package_type == 'event' and not value.is_event_active:
            raise serializers.ValidationError("This event package is not currently active.")

        return value

from datetime import datetime

class CouponGenerateSerializer(serializers.Serializer):
    discount = serializers.IntegerField(min_value=0)
    count = serializers.IntegerField(min_value=1, max_value=1000)
    discount_type = serializers.ChoiceField(
        choices=[("percentage", "Percentage"), ("amount", "Amount")]
    )
    expiry_date = serializers.DateField()
    usage_limit = serializers.IntegerField(min_value=1, required=False)
    codes = serializers.ListField(
        child=serializers.CharField(max_length=20),
        required=False,
        allow_empty=True
    )

class CouponValidateSerializer(serializers.Serializer):
    coupon_code = serializers.CharField(max_length=20)

class CouponSerializer(serializers.ModelSerializer):
    class Meta:
        model = Coupon
        fields = ['id', 'code', "usage_limit" ,'discount', 'discount_type', 'expiry_date', 'is_active']
        required_fields = ['code', 'discount' ,"discount_type" ,"usege_limit" , 'expiry_date']




from rest_framework import serializers
from .models import GiftCard

class GiftCardSerializer(serializers.ModelSerializer):
    class Meta:
        model = GiftCard
        fields = '__all__'
        
class GiftCardValidateSerializer(serializers.Serializer):
    gift_code = serializers.CharField(max_length=20)

class SubscriptionInvoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Subscription_Invoice
        fields = '__all__'