from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from students.models import Student
from .models import UserContact, Contact, ContactRelationship, UserContactStats
from .serializers import BulkContactUploadSerializer, ContactDataSerializer
import json


class ContactModelTests(TestCase):
    """Test cases for Contact models"""

    def setUp(self):
        """Set up test data"""
        self.user1 = User.objects.create_user(
            username='testuser1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create student profiles
        self.student1 = Student.objects.create(
            user=self.user1,
            phone='9876543210'
        )
        self.student2 = Student.objects.create(
            user=self.user2,
            phone='9876543211'
        )

    def test_user_contact_creation(self):
        """Test UserContact model creation"""
        contact = UserContact.objects.create(
            user=self.user1,
            name='Test Contact',
            contact_number='9876543212'
        )

        self.assertEqual(contact.user, self.user1)
        self.assertEqual(contact.name, 'Test Contact')
        self.assertEqual(contact.contact_number, '9876543212')
        self.assertIsNotNone(contact.created_at)

    def test_contact_creation_with_registered_user(self):
        """Test Contact model creation with registered user detection"""
        contact = Contact.objects.create(
            name='Test User 2',
            contact_number='9876543211'  # This matches student2's phone
        )

        self.assertTrue(contact.is_registered_user)
        self.assertEqual(contact.registered_user, self.user2)

    def test_contact_creation_without_registered_user(self):
        """Test Contact model creation without registered user"""
        contact = Contact.objects.create(
            name='Unknown Contact',
            contact_number='9876543999'
        )

        self.assertFalse(contact.is_registered_user)
        self.assertIsNone(contact.registered_user)

    def test_contact_relationship_creation(self):
        """Test ContactRelationship model creation"""
        contact = Contact.objects.create(
            name='Test Contact',
            contact_number='9876543212'
        )

        relationship = ContactRelationship.objects.create(
            user=self.user1,
            contact=contact,
            relationship_type='contact'
        )

        self.assertEqual(relationship.user, self.user1)
        self.assertEqual(relationship.contact, contact)
        self.assertEqual(relationship.relationship_type, 'contact')
        self.assertTrue(relationship.is_active)

    def test_user_contact_stats_creation(self):
        """Test UserContactStats model creation and update"""
        stats = UserContactStats.objects.create(user=self.user1)

        # Create some test data
        contact1 = Contact.objects.create(name='Contact 1', contact_number='9876543212')
        contact2 = Contact.objects.create(name='Contact 2', contact_number='9876543213')

        UserContact.objects.create(user=self.user1, name='Contact 1', contact_number='9876543212')
        UserContact.objects.create(user=self.user1, name='Contact 2', contact_number='9876543213')

        ContactRelationship.objects.create(user=self.user1, contact=contact1)
        ContactRelationship.objects.create(user=self.user1, contact=contact2)

        # Update stats
        stats.update_stats()

        self.assertEqual(stats.total_contacts_uploaded, 2)
        self.assertEqual(stats.active_relationships, 2)


class ContactSerializerTests(TestCase):
    """Test cases for Contact serializers"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.student = Student.objects.create(
            user=self.user,
            phone='9876543210'
        )

    def test_contact_data_serializer_valid(self):
        """Test ContactDataSerializer with valid data"""
        data = {
            'name': 'Test Contact',
            'contact': '9876543211'
        }

        serializer = ContactDataSerializer(data=data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['contact'], '9876543211')

    def test_contact_data_serializer_invalid_contact(self):
        """Test ContactDataSerializer with invalid contact number"""
        data = {
            'name': 'Test Contact',
            'contact': '123'  # Too short
        }

        serializer = ContactDataSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('contact', serializer.errors)

    def test_bulk_contact_upload_serializer_valid(self):
        """Test BulkContactUploadSerializer with valid data"""
        data = {
            'contacts': [
                {'name': 'Contact 1', 'contact': '9876543211'},
                {'name': 'Contact 2', 'contact': '9876543212'}
            ]
        }

        serializer = BulkContactUploadSerializer(data=data)
        self.assertTrue(serializer.is_valid())

    def test_bulk_contact_upload_serializer_duplicate_contacts(self):
        """Test BulkContactUploadSerializer with duplicate contacts"""
        data = {
            'contacts': [
                {'name': 'Contact 1', 'contact': '9876543211'},
                {'name': 'Contact 1 Duplicate', 'contact': '9876543211'}
            ]
        }

        serializer = BulkContactUploadSerializer(data=data)
        self.assertFalse(serializer.is_valid())
        self.assertIn('contacts', serializer.errors)


class ContactAPITests(APITestCase):
    """Test cases for Contact API endpoints"""

    def setUp(self):
        """Set up test data"""
        self.user1 = User.objects.create_user(
            username='testuser1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create student profiles
        self.student1 = Student.objects.create(
            user=self.user1,
            phone='9876543210'
        )
        self.student2 = Student.objects.create(
            user=self.user2,
            phone='9876543211'
        )

        # Set up API client with authentication
        self.client = APIClient()
        refresh = RefreshToken.for_user(self.user1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_bulk_contact_upload_success(self):
        """Test successful bulk contact upload"""
        url = reverse('contacts:bulk_upload')
        data = {
            'contacts': [
                {'name': 'Contact 1', 'contact': '9876543211'},
                {'name': 'Contact 2', 'contact': '9876543212'},
                {'name': 'Contact 3', 'contact': '9876543213'}
            ]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['contacts_uploaded'], 3)

        # Verify contacts were created
        self.assertEqual(UserContact.objects.filter(user=self.user1).count(), 3)
        self.assertEqual(Contact.objects.count(), 3)
        self.assertEqual(ContactRelationship.objects.filter(user=self.user1).count(), 3)

    def test_bulk_contact_upload_invalid_data(self):
        """Test bulk contact upload with invalid data"""
        url = reverse('contacts:bulk_upload')
        data = {
            'contacts': [
                {'name': 'Contact 1', 'contact': '123'},  # Invalid contact number
            ]
        }

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])

    def test_bulk_contact_upload_unauthenticated(self):
        """Test bulk contact upload without authentication"""
        self.client.credentials()  # Remove authentication
        url = reverse('contacts:bulk_upload')
        data = {'contacts': []}

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_get_user_contacts(self):
        """Test retrieving user's contacts"""
        # Create test contacts
        UserContact.objects.create(user=self.user1, name='Contact 1', contact_number='9876543211')
        UserContact.objects.create(user=self.user1, name='Contact 2', contact_number='9876543212')

        url = reverse('contacts:user_contacts')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 2)

    def test_get_contact_relationships(self):
        """Test retrieving user's contact relationships"""
        # Create test data
        contact1 = Contact.objects.create(name='Contact 1', contact_number='9876543211')
        contact2 = Contact.objects.create(name='Contact 2', contact_number='9876543212')

        ContactRelationship.objects.create(user=self.user1, contact=contact1, relationship_type='contact')
        ContactRelationship.objects.create(user=self.user1, contact=contact2, relationship_type='friend')

        url = reverse('contacts:relationships')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 2)

    def test_get_contact_relationships_filtered(self):
        """Test retrieving filtered contact relationships"""
        # Create test data
        contact1 = Contact.objects.create(name='Contact 1', contact_number='9876543211')
        contact2 = Contact.objects.create(name='Contact 2', contact_number='9876543212')

        ContactRelationship.objects.create(user=self.user1, contact=contact1, relationship_type='contact')
        ContactRelationship.objects.create(user=self.user1, contact=contact2, relationship_type='friend')

        url = reverse('contacts:relationships')
        response = self.client.get(url, {'type': 'friend'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['relationship_type'], 'friend')

    def test_update_contact_relationship(self):
        """Test updating a contact relationship"""
        contact = Contact.objects.create(name='Contact 1', contact_number='9876543211')
        relationship = ContactRelationship.objects.create(
            user=self.user1,
            contact=contact,
            relationship_type='contact'
        )

        url = reverse('contacts:update_relationship', kwargs={'relationship_id': relationship.id})
        data = {'relationship_type': 'friend'}

        response = self.client.patch(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Verify update
        relationship.refresh_from_db()
        self.assertEqual(relationship.relationship_type, 'friend')

    def test_contact_search(self):
        """Test contact search functionality"""
        # Create test data
        contact1 = Contact.objects.create(name='John Doe', contact_number='9876543211')
        contact2 = Contact.objects.create(name='Jane Smith', contact_number='9876543212')

        ContactRelationship.objects.create(user=self.user1, contact=contact1)
        ContactRelationship.objects.create(user=self.user1, contact=contact2)

        url = reverse('contacts:search')
        response = self.client.get(url, {'query': 'John'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['contact_info']['name'], 'John Doe')

    def test_get_user_contact_stats(self):
        """Test retrieving user contact statistics"""
        url = reverse('contacts:stats')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('total_contacts_uploaded', response.data['data'])
        self.assertIn('active_relationships', response.data['data'])

    def test_get_mutual_contacts(self):
        """Test retrieving mutual contacts"""
        # Create a contact that represents user2
        contact_user2 = Contact.objects.create(
            name='Test User 2',
            contact_number='9876543211'  # user2's phone
        )

        # User1 has user2 in contacts
        ContactRelationship.objects.create(user=self.user1, contact=contact_user2)

        # Create a contact that represents user1 for user2
        contact_user1 = Contact.objects.create(
            name='Test User 1',
            contact_number='9876543210'  # user1's phone
        )

        # User2 has user1 in contacts
        ContactRelationship.objects.create(user=self.user2, contact=contact_user1)

        url = reverse('contacts:mutual_contacts')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

    def test_update_contact_status(self):
        """Test updating contact status"""
        contact = Contact.objects.create(name='Contact 1', contact_number='9876543211')
        relationship = ContactRelationship.objects.create(user=self.user1, contact=contact)

        url = reverse('contacts:update_status', kwargs={'contact_id': contact.id})
        data = {'is_active': False}

        response = self.client.patch(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Verify update
        relationship.refresh_from_db()
        self.assertFalse(relationship.is_active)
