from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from blogs.models import BlogPost


class BlogPostSitemap(Sitemap):
    changefreq = "weekly"
    priority = 0.8

    def items(self):
        return BlogPost.objects.filter(approval_status="approved")

    def location(self, obj):
        return reverse("blogpost-detail", args=[obj.pk])

    def lastmod(self, obj):
        return obj.updated_date
