from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied
from .models import ContributorProfile
from customrcare.models import CustomrcareProfile


class IsContributorUser(BasePermission):
    def has_permission(self, request, view):
        user = request.user

        if user.is_authenticated:
            try:
                contributor = ContributorProfile.objects.get(user=user)
                if contributor.role == "contributor":
                    return True
                raise PermissionDenied(
                    detail="You do not have permission for this resource."
                )
            except ContributorProfile.DoesNotExist:
                raise PermissionDenied(detail="Contributor profile not found.")
        raise PermissionDenied(detail="Authentication required.")

class IsCustomerCareOrReadOnly(BasePermission):
    """
    Custom permission to only allow customercare users to edit it.
    Read-only permissions are allowed to any request.
    """

    def has_permission(self, request, view):
        if request.method in ('GET'):
            return True
        return CustomrcareProfile.objects.filter(user=request.user).exists()


class IsContributorForPopupBanner(BasePermission):
    """
    Permission class for PopupBanner operations by contributors.
    Contributors can create banners and view/edit their own banners only.
    """

    def has_permission(self, request, view):
        """Check if user is authenticated and is a contributor"""
        if not request.user.is_authenticated:
            raise PermissionDenied(detail="Authentication required.")

        try:
            contributor = ContributorProfile.objects.get(user=request.user)
            if contributor.role == "contributor":
                return True
            raise PermissionDenied(
                detail="You do not have permission for this resource."
            )
        except ContributorProfile.DoesNotExist:
            raise PermissionDenied(detail="Contributor profile not found.")

    def has_object_permission(self, request, view, obj):
        """Contributors can only access their own banners"""
        return obj.created_by == request.user


class IsCustomerCareForPopupBanner(BasePermission):
    """
    Permission class for PopupBanner operations by customer care.
    Customer care can view all banners and approve/reject them.
    """

    def has_permission(self, request, view):
        """Check if user is authenticated and is customer care"""
        if not request.user.is_authenticated:
            raise PermissionDenied(detail="Authentication required.")

        try:
            care_profile = CustomrcareProfile.objects.get(user=request.user)
            if care_profile.role == "customrcare":
                return True
            raise PermissionDenied(
                detail="You do not have permission for this resource."
            )
        except CustomrcareProfile.DoesNotExist:
            raise PermissionDenied(detail="Customer care profile not found.")

    def has_object_permission(self, request, view, obj):
        """Customer care can access all banners"""
        return True


class IsAdminForPopupBanner(BasePermission):
    """
    Permission class for PopupBanner operations by admin.
    Admin has full control over all banners.
    """

    def has_permission(self, request, view):
        """Check if user is authenticated and is admin/staff"""
        if not request.user.is_authenticated:
            raise PermissionDenied(detail="Authentication required.")

        if request.user.is_staff or request.user.is_superuser:
            return True

        raise PermissionDenied(
            detail="Admin privileges required for this resource."
        )

    def has_object_permission(self, request, view, obj):
        """Admin can access all banners"""
        return True


class IsContributorOrCustomerCareOrAdmin(BasePermission):
    """
    Combined permission class that allows access based on user role:
    - Contributors: can create and view own banners
    - Customer Care: can view all and approve/reject
    - Admin: full control
    """

    def has_permission(self, request, view):
        """Check if user has any of the required roles"""
        if not request.user.is_authenticated:
            raise PermissionDenied(detail="Authentication required.")

        # Check if admin
        if request.user.is_staff or request.user.is_superuser:
            return True

        # Check if customer care
        if CustomrcareProfile.objects.filter(user=request.user, role="customrcare").exists():
            return True

        # Check if contributor
        if ContributorProfile.objects.filter(user=request.user, role="contributor").exists():
            return True

        raise PermissionDenied(
            detail="You do not have permission for this resource."
        )

    def has_object_permission(self, request, view, obj):
        """Object-level permissions based on user role"""
        user = request.user

        # Admin has full access
        if user.is_staff or user.is_superuser:
            return True

        # Customer care has full access
        if CustomrcareProfile.objects.filter(user=user, role="customrcare").exists():
            return True

        # Contributors can only access their own banners
        if ContributorProfile.objects.filter(user=user, role="contributor").exists():
            return obj.created_by == user

        return False


class IsPublicReadOnly(BasePermission):
    """
    Permission class for public API - read-only access to active banners
    """

    def has_permission(self, request, view):
        """Allow read-only access to everyone"""
        return request.method in ['GET', 'HEAD', 'OPTIONS']

    def has_object_permission(self, request, view, obj):
        """Only allow access to active banners"""
        return request.method in ['GET', 'HEAD', 'OPTIONS'] and obj.is_active