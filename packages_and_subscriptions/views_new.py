"""
New clean implementation of subscription views using PaymentService
"""
import logging
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone

from .models import Package, Subscription, Subscription_Invoice, FailedPaymentLog
from .serializers import PackageSerializer, SubscriptionSerializer
from students.models import Student
from .payment_service import PaymentService

logger = logging.getLogger(__name__)


class NewSubscriptionCreateView(APIView):
    """
    New clean subscription creation view using PaymentService
    """
    
    @transaction.atomic
    def post(self, request):
        """Create a new subscription with payment processing"""
        
        # Extract request data
        student_id = request.data.get("student")
        package_id = request.data.get("package")
        coupon_code = request.data.get("coupon_code")
        gift_card_code = request.data.get("gift_card_code")
        gift_card_pin = request.data.get("gift_card_pin")
        
        logger.info(f"Subscription creation request: student={student_id}, package={package_id}")
        
        # Validate required fields
        if not student_id or not package_id:
            return Response({
                "error": "Student ID and Package ID are required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get student and package
        try:
            student = get_object_or_404(Student, id=student_id)
            package = get_object_or_404(Package, id=package_id, is_active=True)
        except Exception as e:
            logger.error(f"Student or package not found: {str(e)}")
            return Response({
                "error": "Student or package not found"
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Validate package availability
        if package.package_type == 'event' and not package.is_event_active:
            logger.warning(f"Event package {package_id} is not currently active")
            return Response({
                "error": "This event is not currently available"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Initialize payment service
        payment_service = PaymentService()
        
        try:
            # Calculate pricing
            pricing_info = payment_service.calculate_final_price(
                package=package,
                coupon_code=coupon_code,
                gift_card_code=gift_card_code,
                gift_card_pin=gift_card_pin,
                student=student
            )
            
            final_price = pricing_info['final_price']
            logger.info(f"Final price calculated: ₹{final_price}")
            
            # Create Razorpay order if payment is required
            razorpay_order = None
            if final_price > 0:
                razorpay_order = payment_service.create_razorpay_order(
                    amount=final_price,
                    student=student,
                    package=package
                )
            
            # Create subscription
            subscription, invoice = payment_service.create_subscription(
                student=student,
                package=package,
                pricing_info=pricing_info,
                razorpay_order=razorpay_order
            )
            
            # Prepare response
            response_data = {
                "success": True,
                "subscription_id": subscription.id,
                "invoice_id": invoice.id,
                "final_price": float(final_price),
                "currency": "INR",
                "is_free": final_price == 0,
                "package_type": package.package_type,
                "package_name": package.name
            }
            
            # Add Razorpay order details if payment is required
            if razorpay_order:
                response_data.update({
                    "razorpay_order_id": razorpay_order['id'],
                    "razorpay_key": payment_service.client.auth[0] if payment_service.client else None,
                    "amount_in_paise": int(final_price * 100)
                })
            
            logger.info(f"Subscription {subscription.id} created successfully for student {student.id}")
            
            return Response(response_data, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Subscription creation failed: {str(e)}")
            return Response({
                "error": str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class NewPaymentVerificationView(APIView):
    """
    New clean payment verification view
    """
    
    @transaction.atomic
    def post(self, request):
        """Verify payment and activate subscription"""
        
        # Extract payment data
        razorpay_order_id = request.data.get("razorpay_order_id")
        razorpay_payment_id = request.data.get("razorpay_payment_id")
        razorpay_signature = request.data.get("razorpay_signature")
        subscription_id = request.data.get("subscription_id")
        
        logger.info(f"Payment verification request: subscription={subscription_id}, order={razorpay_order_id}")
        
        # Validate required fields
        if not all([razorpay_order_id, razorpay_payment_id, razorpay_signature, subscription_id]):
            return Response({
                "error": "Missing payment verification data"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get subscription
        try:
            subscription = Subscription.objects.select_for_update().get(id=subscription_id)
        except Subscription.DoesNotExist:
            logger.error(f"Subscription not found: {subscription_id}")
            return Response({
                "error": "Subscription not found"
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check if already activated
        if subscription.is_active:
            logger.info(f"Subscription {subscription_id} already active")
            return Response({
                "success": True,
                "message": "Subscription already active"
            }, status=status.HTTP_200_OK)
        
        # Verify order ID matches
        if subscription.pg_order_id != razorpay_order_id:
            logger.error(f"Order ID mismatch: expected {subscription.pg_order_id}, got {razorpay_order_id}")
            return Response({
                "error": "Order ID mismatch"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Initialize payment service
        payment_service = PaymentService()
        
        try:
            # Verify payment signature
            payment_service.verify_payment_signature(
                order_id=razorpay_order_id,
                payment_id=razorpay_payment_id,
                signature=razorpay_signature
            )
            
            # Activate subscription
            payment_service.activate_subscription(
                subscription=subscription,
                payment_id=razorpay_payment_id,
                signature=razorpay_signature
            )
            
            logger.info(f"Payment verified and subscription {subscription_id} activated")
            
            return Response({
                "success": True,
                "message": "Payment verified and subscription activated",
                "subscription_id": subscription.id,
                "package_name": subscription.package.name,
                "end_date": subscription.end_date.isoformat() if subscription.end_date else None
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Payment verification failed: {str(e)}")
            
            # Log failed payment
            payment_service.log_failed_payment(
                student=subscription.student,
                order_id=razorpay_order_id,
                payment_id=razorpay_payment_id,
                reason=str(e)
            )
            
            return Response({
                "error": "Payment verification failed",
                "details": str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class SubscriptionStatusView(APIView):
    """
    Check subscription status for a student
    """
    
    def get(self, request, student_id):
        """Get subscription status for a student"""
        
        try:
            student = get_object_or_404(Student, id=student_id)
        except Exception:
            return Response({
                "error": "Student not found"
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get subscription information
        active_subscriptions = student.active_subscriptions
        validity_subscriptions = student.validity_subscriptions
        event_subscriptions = student.event_subscriptions
        
        response_data = {
            "student_id": student.id,
            "student_name": f"{student.user.first_name} {student.user.last_name}",
            "current_status": student.current_subscription_status,
            "total_active_subscriptions": active_subscriptions.count(),
            "validity_subscriptions": validity_subscriptions.count(),
            "event_subscriptions": event_subscriptions.count(),
            "subscriptions": []
        }
        
        # Add subscription details
        for subscription in active_subscriptions:
            sub_data = {
                "id": subscription.id,
                "package_name": subscription.package.name,
                "package_type": subscription.package.package_type,
                "start_date": subscription.start_date.isoformat() if subscription.start_date else None,
                "end_date": subscription.end_date.isoformat() if subscription.end_date else None,
                "is_valid": subscription.is_valid_subscription(),
                "final_price": float(subscription.final_price),
                "is_active": subscription.is_active
            }
            response_data["subscriptions"].append(sub_data)
        
        return Response(response_data, status=status.HTTP_200_OK)


class PackageAccessView(APIView):
    """
    Check if student has access to specific content
    """
    
    def post(self, request):
        """Check content access for a student"""
        
        student_id = request.data.get("student_id")
        content_id = request.data.get("content_id")
        
        if not student_id:
            return Response({
                "error": "Student ID is required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            student = get_object_or_404(Student, id=student_id)
        except Exception:
            return Response({
                "error": "Student not found"
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check access
        has_access = student.has_access_to_content(content_id)
        
        response_data = {
            "student_id": student.id,
            "content_id": content_id,
            "has_access": has_access,
            "current_status": student.current_subscription_status
        }
        
        # Add details about which subscriptions provide access
        if has_access:
            providing_subscriptions = []
            for subscription in student.active_subscriptions:
                if subscription.can_access_content(content_id):
                    providing_subscriptions.append({
                        "subscription_id": subscription.id,
                        "package_name": subscription.package.name,
                        "package_type": subscription.package.package_type
                    })
            response_data["providing_subscriptions"] = providing_subscriptions
        
        return Response(response_data, status=status.HTTP_200_OK)
