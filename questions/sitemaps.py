from django.contrib.sitemaps import Sitemap
from django.urls import reverse

from questions.models import Course, SubCourse



class CourseSitemap(Sitemap):
    changefreq = "monthly"
    priority = 0.7

    def items(self):
        return Course.objects.all()  

    def location(self, obj):
        return reverse("course-detail", args=[obj.pk])

    def lastmod(self, obj):
        return obj.updated_date  


class SubCourseSitemap(Sitemap):
    changefreq = "monthly"
    priority = 0.6

    def items(self):
        return SubCourse.objects.all()  

    def location(self, obj):
        return reverse("subcourse-detail", args=[obj.pk])

    def lastmod(self, obj):
        return obj.updated_date 
