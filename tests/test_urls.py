#!/usr/bin/env python3
"""
Test script to check URL configurations.
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append('/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.urls import reverse, resolve
from django.test import RequestFactory
from customrcare.views import CustomrcareQuestionListView, CustomrcareQuestionSearchView

def test_url_patterns():
    """Test URL patterns for customer care questions."""
    
    print("🔍 Testing URL patterns...")
    
    try:
        # Test the new questions list endpoint
        url = reverse('customrcare-question-list')
        print(f"✅ customrcare-question-list URL: {url}")
        
        # Test URL resolution
        resolved = resolve(url)
        print(f"✅ Resolved view: {resolved.func.view_class.__name__}")
        
    except Exception as e:
        print(f"❌ Error with customrcare-question-list: {str(e)}")
    
    try:
        # Test the search endpoint
        search_url = reverse('customrcare-question-search')
        print(f"✅ customrcare-question-search URL: {search_url}")
        
        # Test URL resolution
        resolved = resolve(search_url)
        print(f"✅ Resolved view: {resolved.func.view_class.__name__}")
        
    except Exception as e:
        print(f"❌ Error with customrcare-question-search: {str(e)}")

def test_view_imports():
    """Test that views can be imported correctly."""
    
    print("\n🔍 Testing view imports...")
    
    try:
        from customrcare.views import CustomrcareQuestionListView
        print("✅ CustomrcareQuestionListView imported successfully")
    except ImportError as e:
        print(f"❌ Error importing CustomrcareQuestionListView: {str(e)}")
    
    try:
        from customrcare.views import CustomrcareQuestionSearchView
        print("✅ CustomrcareQuestionSearchView imported successfully")
    except ImportError as e:
        print(f"❌ Error importing CustomrcareQuestionSearchView: {str(e)}")

def test_view_functionality():
    """Test basic view functionality."""
    
    print("\n🔍 Testing view functionality...")
    
    factory = RequestFactory()
    
    try:
        # Test CustomrcareQuestionListView
        request = factory.get('/api/customrcare/questions/')
        view = CustomrcareQuestionListView()
        view.request = request
        
        print("✅ CustomrcareQuestionListView instantiated successfully")
        
    except Exception as e:
        print(f"❌ Error with CustomrcareQuestionListView: {str(e)}")
    
    try:
        # Test CustomrcareQuestionSearchView
        request = factory.get('/api/customrcare/questions/search/')
        view = CustomrcareQuestionSearchView()
        view.request = request
        
        print("✅ CustomrcareQuestionSearchView instantiated successfully")
        
    except Exception as e:
        print(f"❌ Error with CustomrcareQuestionSearchView: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting URL configuration test...")
    
    try:
        test_view_imports()
        test_url_patterns()
        test_view_functionality()
        
        print("\n✅ URL configuration test completed!")
        
    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
