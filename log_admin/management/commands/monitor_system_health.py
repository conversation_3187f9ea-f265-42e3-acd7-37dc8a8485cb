"""
Management command to monitor system health and log metrics
"""

import psutil
import time
from django.core.management.base import BaseCommand
from django.db import connection
from django.utils import timezone
from django.contrib.sessions.models import Session
from log_admin.models import SystemHealthLog, PerformanceLog, ErrorLog
from log_admin.utils import LoggingUtils
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Monitor system health and log metrics'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='Monitoring interval in seconds (default: 60)',
        )
        parser.add_argument(
            '--duration',
            type=int,
            default=0,
            help='Total monitoring duration in minutes (0 = run once)',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )
    
    def handle(self, *args, **options):
        interval = options['interval']
        duration = options['duration']
        verbose = options['verbose']
        
        if duration > 0:
            self.stdout.write(
                f"Starting system health monitoring for {duration} minutes "
                f"with {interval}s intervals..."
            )
            end_time = time.time() + (duration * 60)
        else:
            self.stdout.write("Running single system health check...")
            end_time = time.time() + 1
        
        try:
            while time.time() < end_time:
                self.collect_system_metrics(verbose)
                
                if duration > 0 and time.time() < end_time:
                    if verbose:
                        self.stdout.write(f"Waiting {interval} seconds...")
                    time.sleep(interval)
                else:
                    break
            
            self.stdout.write(
                self.style.SUCCESS('System health monitoring completed')
            )
            
        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('Monitoring interrupted by user')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Monitoring failed: {str(e)}')
            )
            logger.error(f"System health monitoring failed: {e}")
            raise
    
    def collect_system_metrics(self, verbose=False):
        """Collect and log system health metrics"""
        timestamp = timezone.now()
        
        if verbose:
            self.stdout.write(f"Collecting metrics at {timestamp}")
        
        # CPU Usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.log_metric('CPU_USAGE', cpu_percent, '%', 80, 95, verbose)
        
        # Memory Usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        self.log_metric('MEMORY_USAGE', memory_percent, '%', 80, 90, verbose)
        
        # Disk Usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        self.log_metric('DISK_USAGE', disk_percent, '%', 80, 90, verbose)
        
        # Database Connections
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM pg_stat_activity")
                db_connections = cursor.fetchone()[0]
            self.log_metric('DATABASE_CONNECTIONS', db_connections, 'count', 50, 80, verbose)
        except Exception:
            # Fallback for SQLite or other databases
            db_connections = 1
            self.log_metric('DATABASE_CONNECTIONS', db_connections, 'count', 10, 20, verbose)
        
        # Active Sessions
        active_sessions = Session.objects.filter(
            expire_date__gt=timezone.now()
        ).count()
        self.log_metric('ACTIVE_SESSIONS', active_sessions, 'count', 100, 200, verbose)
        
        # Average Response Time (last hour)
        last_hour = timestamp - timedelta(hours=1)
        from django.db.models import Avg
        avg_response_time = PerformanceLog.objects.filter(
            created_at__gte=last_hour
        ).aggregate(avg_time=Avg('duration'))['avg_time'] or 0
        self.log_metric('RESPONSE_TIME', avg_response_time, 'seconds', 2.0, 5.0, verbose)
        
        # Error Rate (last hour)
        total_requests = PerformanceLog.objects.filter(created_at__gte=last_hour).count()
        error_count = ErrorLog.objects.filter(timestamp__gte=last_hour).count()
        error_rate = (error_count / max(total_requests, 1)) * 100
        self.log_metric('ERROR_RATE', error_rate, '%', 5.0, 10.0, verbose)
        
        # Request Throughput (requests per minute in last hour)
        throughput = total_requests / 60 if total_requests > 0 else 0
        self.log_metric('THROUGHPUT', throughput, 'req/min', None, None, verbose)
    
    def log_metric(self, metric_type, value, unit, warning_threshold=None, 
                   critical_threshold=None, verbose=False):
        """Log a system health metric"""
        
        # Determine status
        status = 'OK'
        if critical_threshold and value >= critical_threshold:
            status = 'CRITICAL'
        elif warning_threshold and value >= warning_threshold:
            status = 'WARNING'
        
        # Create log entry
        SystemHealthLog.objects.create(
            metric_type=metric_type,
            value=value,
            unit=unit,
            threshold_warning=warning_threshold,
            threshold_critical=critical_threshold,
            status=status,
            additional_info={
                'collected_at': timezone.now().isoformat(),
                'hostname': psutil.os.uname().nodename if hasattr(psutil.os, 'uname') else 'unknown'
            }
        )
        
        if verbose:
            color = self.style.SUCCESS
            if status == 'WARNING':
                color = self.style.WARNING
            elif status == 'CRITICAL':
                color = self.style.ERROR
            
            self.stdout.write(
                color(f"  {metric_type}: {value}{unit} ({status})")
            )
        
        # Log critical alerts
        if status == 'CRITICAL':
            logger.critical(
                f"CRITICAL SYSTEM ALERT: {metric_type} = {value}{unit} "
                f"(threshold: {critical_threshold}{unit})"
            )
            
            # You could add email/SMS alerting here
            # send_critical_alert(metric_type, value, unit, critical_threshold)
