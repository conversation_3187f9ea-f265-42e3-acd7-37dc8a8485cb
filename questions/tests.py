from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from django.contrib.auth.models import User
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from PIL import Image
import io
import tempfile
import os

from .models import Question, Option, MasterQuestion, MasterOption, Subject, Topic, SubTopic, Course, SubCourse
from contributor.models import ContributorProfile


class ImageAttachmentTestCase(APITestCase):
    """Test cases for image attachment functionality in questions, options, and explanations."""

    def setUp(self):
        """Set up test data."""
        # Create test user and contributor profile
        self.user = User.objects.create_user(
            username='testcontributor',
            email='<EMAIL>',
            password='testpass123'
        )
        self.contributor = ContributorProfile.objects.create(
            user=self.user,
            role='contributor'
        )

        # Create test subject, topic, course
        self.subject = Subject.objects.create(name='Test Subject')
        self.topic = Topic.objects.create(subject=self.subject, name='Test Topic')
        self.subtopic = SubTopic.objects.create(topic=self.topic, name='Test SubTopic')
        self.course = Course.objects.create(name='Test Course')
        self.subcourse = SubCourse.objects.create(course=self.course, name='Test SubCourse')

        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

    def create_test_image(self, name='test_image.jpg', format='JPEG'):
        """Create a test image file."""
        image = Image.new('RGB', (100, 100), color='red')
        image_file = io.BytesIO()
        image.save(image_file, format=format)
        image_file.seek(0)
        return SimpleUploadedFile(
            name=name,
            content=image_file.getvalue(),
            content_type=f'image/{format.lower()}'
        )

    def test_question_with_attachments(self):
        """Test creating and updating questions with image attachments."""
        # Test image files
        question_image = self.create_test_image('question.jpg')
        explanation_image = self.create_test_image('explanation.jpg')
        reason_image = self.create_test_image('reason.jpg')

        # Create question with attachments
        question_data = {
            'content': 'Test question with image',
            'difficulty': 1,
            'author': self.contributor.id,
            'subject': [self.subject.id],
            'topic': [self.topic.id],
            'sub_topic': [self.subtopic.id],
            'course': [self.course.id],
            'subcourse': [self.subcourse.id],
            'explanation': 'Test explanation with image',
            'reason': 'Test reason with image',
            'attachments': question_image,
            'explanation_attachment': explanation_image,
            'reason_document': reason_image,
        }

        response = self.client.post('/api/questions/', question_data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify question was created with attachments
        question = Question.objects.get(question_id=response.data['question_id'])
        self.assertTrue(question.attachments)
        self.assertTrue(question.explanation_attachment)
        self.assertTrue(question.reason_document)
        self.assertEqual(question.explanation, 'Test explanation with image')
        self.assertEqual(question.reason, 'Test reason with image')

        print(f"✅ Question created with attachments: {question.attachments.url}")
        print(f"✅ Explanation attachment: {question.explanation_attachment.url}")
        print(f"✅ Reason document: {question.reason_document.url}")

    def test_option_with_attachments(self):
        """Test creating options with image attachments."""
        # First create a question
        question = Question.objects.create(
            content='Test question for options',
            difficulty=1,
            author=self.contributor
        )
        question.subject.add(self.subject)
        question.topic.add(self.topic)
        question.course.add(self.course)

        # Create option with attachment
        option_image = self.create_test_image('option.jpg')
        option_data = {
            'question': question.question_id,
            'option_text': 'Test option with image',
            'is_correct': True,
            'attachments': option_image,
        }

        response = self.client.post(f'/api/questions/{question.slug}/options/', option_data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify option was created with attachment
        option = Option.objects.get(option_id=response.data['option_id'])
        self.assertTrue(option.attachments)
        self.assertEqual(option.option_text, 'Test option with image')
        self.assertTrue(option.is_correct)

        print(f"✅ Option created with attachment: {option.attachments.url}")

    def test_master_question_with_attachments(self):
        """Test creating master questions with image attachments."""
        master_image = self.create_test_image('master_question.jpg')
        master_reason_image = self.create_test_image('master_reason.jpg')

        master_question_data = {
            'title': 'Test Master Question',
            'passage_content': 'This is a test passage for master question',
            'author': self.contributor.id,
            'attachments': master_image,
            'reason': 'Test reason for master question',
            'reason_document': master_reason_image,
        }

        response = self.client.post('/api/master-questions/', master_question_data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify master question was created with attachments
        master_question = MasterQuestion.objects.get(master_question_id=response.data['master_question_id'])
        self.assertTrue(master_question.attachments)
        self.assertTrue(master_question.reason_document)
        self.assertEqual(master_question.reason, 'Test reason for master question')

        print(f"✅ Master question created with attachments: {master_question.attachments.url}")
        print(f"✅ Master question reason document: {master_question.reason_document.url}")

    def test_master_option_with_attachments(self):
        """Test creating master options with image attachments."""
        master_option_image = self.create_test_image('master_option.jpg')
        master_option_reason_image = self.create_test_image('master_option_reason.jpg')

        master_option_data = {
            'title': 'Test Master Option',
            'option_content': 'This is a test option content for master option',
            'conditions': 'Test conditions for master option',
            'author': self.contributor.id,
            'attachments': master_option_image,
            'reason': 'Test reason for master option',
            'reason_document': master_option_reason_image,
        }

        response = self.client.post('/api/master-options/', master_option_data, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        # Verify master option was created with attachments
        master_option = MasterOption.objects.get(master_option_id=response.data['master_option_id'])
        self.assertTrue(master_option.attachments)
        self.assertTrue(master_option.reason_document)
        self.assertEqual(master_option.reason, 'Test reason for master option')

        print(f"✅ Master option created with attachments: {master_option.attachments.url}")
        print(f"✅ Master option reason document: {master_option.reason_document.url}")

    def tearDown(self):
        """Clean up test files."""
        # Clean up uploaded test files
        for question in Question.objects.all():
            if question.attachments:
                if os.path.exists(question.attachments.path):
                    os.remove(question.attachments.path)
            if question.explanation_attachment:
                if os.path.exists(question.explanation_attachment.path):
                    os.remove(question.explanation_attachment.path)
            if question.reason_document:
                if os.path.exists(question.reason_document.path):
                    os.remove(question.reason_document.path)

        for option in Option.objects.all():
            if option.attachments:
                if os.path.exists(option.attachments.path):
                    os.remove(option.attachments.path)

        for master_question in MasterQuestion.objects.all():
            if master_question.attachments:
                if os.path.exists(master_question.attachments.path):
                    os.remove(master_question.attachments.path)
            if master_question.reason_document:
                if os.path.exists(master_question.reason_document.path):
                    os.remove(master_question.reason_document.path)

        for master_option in MasterOption.objects.all():
            if master_option.attachments:
                if os.path.exists(master_option.attachments.path):
                    os.remove(master_option.attachments.path)
            if master_option.reason_document:
                if os.path.exists(master_option.reason_document.path):
                    os.remove(master_option.reason_document.path)
