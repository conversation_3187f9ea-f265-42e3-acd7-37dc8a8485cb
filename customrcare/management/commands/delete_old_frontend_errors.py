# from django.core.management.base import BaseCommand
# from django.utils import timezone
# from datetime import timedelta
# from customrcare.models import FrontendError

# class Command(BaseCommand):
#     help = 'Delete FrontendError records older than 48 hours'

#     def handle(self, *args, **kwargs):
#         period = timezone.now() - timedelta(hours=48)
#         deleted_count, _ = FrontendError.objects.filter(created_at__lt=period).delete()
#         self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_count} old FrontendError records')) 