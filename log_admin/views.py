from django.shortcuts import render
from django.db import connection
from django.utils import timezone
from django.db.models import Count, Avg, <PERSON>, Min, Q
from datetime import timedelta
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuth<PERSON>icated, IsAdminUser
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status, generics, filters
from rest_framework.pagination import PageNumberPagination
from django_filters.rest_framework import DjangoFilterBackend
import json

from .models import (
    PerformanceLog, ErrorLog, UserActivity, APIAccessLog,
    DatabaseQueryLog, AuthenticationLog, SecurityIncident,
    SystemHealthLog, LogConfig
)
from .utils import LogAnalytics, LogCleanup, LoggingUtils
from .serializers import (
    PerformanceLogSerializer, ErrorLogSerializer, UserActivitySerializer,
    APIAccessLogSerializer, DatabaseQueryLogSerializer, AuthenticationLogSerializer,
    SecurityIncidentSerializer, SystemHealthLogSerializer, LogConfigSerializer
)

# Create your views here.

@api_view(['GET'])
@permission_classes([IsAdminUser])
def health_check(request):
    """System health check endpoint"""
    try:
        db_check = connection.ensure_connection()

        # Get basic system metrics
        now = timezone.now()
        last_hour = now - timedelta(hours=1)

        metrics = {
            "status": "ok",
            "database": "connected",
            "timestamp": now.isoformat(),
            "recent_activity": {
                "errors_last_hour": ErrorLog.objects.filter(timestamp__gte=last_hour).count(),
                "requests_last_hour": PerformanceLog.objects.filter(created_at__gte=last_hour).count(),
                "active_users_last_hour": UserActivity.objects.filter(
                    timestamp__gte=last_hour
                ).values('user').distinct().count(),
            }
        }

        return Response(metrics, status=200)
    except Exception as e:
        return Response({
            "status": "error",
            "message": str(e),
            "timestamp": timezone.now().isoformat()
        }, status=500)


class LogPagination(PageNumberPagination):
    """Custom pagination for log endpoints"""
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 1000


class PerformanceLogListView(generics.ListAPIView):
    """List performance logs with filtering and search"""
    queryset = PerformanceLog.objects.all().select_related('user')
    serializer_class = PerformanceLogSerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['method', 'status_code', 'user']
    search_fields = ['path', 'user__username', 'ip_address']
    ordering_fields = ['created_at', 'duration', 'db_queries_count']
    ordering = ['-created_at']


class ErrorLogListView(generics.ListAPIView):
    """List error logs with filtering and search"""
    queryset = ErrorLog.objects.all().select_related('user', 'resolved_by')
    serializer_class = ErrorLogSerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['error_type', 'severity', 'resolved', 'user']
    search_fields = ['view_name', 'error_message', 'user__username', 'path']
    ordering_fields = ['timestamp', 'severity']
    ordering = ['-timestamp']


class UserActivityListView(generics.ListAPIView):
    """List user activities with filtering and search"""
    queryset = UserActivity.objects.all().select_related('user')
    serializer_class = UserActivitySerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['activity_type', 'success', 'user']
    search_fields = ['action', 'description', 'user__username', 'path']
    ordering_fields = ['timestamp']
    ordering = ['-timestamp']


class APIAccessLogListView(generics.ListAPIView):
    """List API access logs with filtering and search"""
    queryset = APIAccessLog.objects.all().select_related('user')
    serializer_class = APIAccessLogSerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['method', 'response_status', 'rate_limited', 'user']
    search_fields = ['endpoint', 'user__username', 'ip_address']
    ordering_fields = ['timestamp', 'response_time']
    ordering = ['-timestamp']


class DatabaseQueryLogListView(generics.ListAPIView):
    """List database query logs with filtering and search"""
    queryset = DatabaseQueryLog.objects.all().select_related('user')
    serializer_class = DatabaseQueryLogSerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['query_type', 'slow_query', 'user']
    search_fields = ['table_name', 'view_name', 'user__username']
    ordering_fields = ['timestamp', 'execution_time']
    ordering = ['-timestamp']


class AuthenticationLogListView(generics.ListAPIView):
    """List authentication logs with filtering and search"""
    queryset = AuthenticationLog.objects.all().select_related('user')
    serializer_class = AuthenticationLogSerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['event_type', 'success', 'user']
    search_fields = ['username_attempted', 'user__username', 'ip_address']
    ordering_fields = ['timestamp']
    ordering = ['-timestamp']


class SecurityIncidentListView(generics.ListAPIView):
    """List security incidents with filtering and search"""
    queryset = SecurityIncident.objects.all().select_related('user', 'resolved_by')
    serializer_class = SecurityIncidentSerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['incident_type', 'severity', 'blocked', 'resolved']
    search_fields = ['description', 'ip_address', 'user__username', 'path']
    ordering_fields = ['timestamp', 'severity']
    ordering = ['-timestamp']


class SystemHealthLogListView(generics.ListAPIView):
    """List system health logs with filtering and search"""
    queryset = SystemHealthLog.objects.all()
    serializer_class = SystemHealthLogSerializer
    permission_classes = [IsAdminUser]
    pagination_class = LogPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['metric_type', 'status']
    search_fields = ['metric_type']
    ordering_fields = ['timestamp', 'value']
    ordering = ['-timestamp']


class LogAnalyticsView(APIView):
    """Analytics dashboard for all log types"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get comprehensive analytics data"""
        days = int(request.query_params.get('days', 7))

        try:
            analytics_data = {
                'period_days': days,
                'generated_at': timezone.now().isoformat(),
                'error_summary': LogAnalytics.get_error_summary(days),
                'performance_summary': LogAnalytics.get_performance_summary(days),
                'user_activity_summary': LogAnalytics.get_user_activity_summary(days),
                'security_summary': LogAnalytics.get_security_summary(days),
            }

            return Response(analytics_data)
        except Exception as e:
            return Response({
                'error': 'Failed to generate analytics',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogDashboardView(APIView):
    """Real-time dashboard data"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get real-time dashboard metrics"""
        try:
            now = timezone.now()
            last_hour = now - timedelta(hours=1)
            last_24h = now - timedelta(hours=24)

            # Recent activity metrics
            dashboard_data = {
                'timestamp': now.isoformat(),
                'real_time_metrics': {
                    'errors_last_hour': ErrorLog.objects.filter(timestamp__gte=last_hour).count(),
                    'critical_errors_last_hour': ErrorLog.objects.filter(
                        timestamp__gte=last_hour, severity='CRITICAL'
                    ).count(),
                    'requests_last_hour': PerformanceLog.objects.filter(
                        created_at__gte=last_hour
                    ).count(),
                    'slow_requests_last_hour': PerformanceLog.objects.filter(
                        created_at__gte=last_hour, duration__gt=2.0
                    ).count(),
                    'active_users_last_hour': UserActivity.objects.filter(
                        timestamp__gte=last_hour
                    ).values('user').distinct().count(),
                    'security_incidents_last_hour': SecurityIncident.objects.filter(
                        timestamp__gte=last_hour
                    ).count(),
                },
                'daily_metrics': {
                    'total_errors_24h': ErrorLog.objects.filter(timestamp__gte=last_24h).count(),
                    'total_requests_24h': PerformanceLog.objects.filter(
                        created_at__gte=last_24h
                    ).count(),
                    'unique_users_24h': UserActivity.objects.filter(
                        timestamp__gte=last_24h
                    ).values('user').distinct().count(),
                    'avg_response_time_24h': PerformanceLog.objects.filter(
                        created_at__gte=last_24h
                    ).aggregate(avg_time=Avg('duration'))['avg_time'],
                },
                'system_status': {
                    'unresolved_errors': ErrorLog.objects.filter(resolved=False).count(),
                    'unresolved_security_incidents': SecurityIncident.objects.filter(
                        resolved=False
                    ).count(),
                    'recent_critical_alerts': SystemHealthLog.objects.filter(
                        timestamp__gte=last_hour, status='CRITICAL'
                    ).count(),
                }
            }

            return Response(dashboard_data)
        except Exception as e:
            return Response({
                'error': 'Failed to generate dashboard data',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogConfigView(APIView):
    """Enhanced logging configuration management"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get current active logging configuration"""
        try:
            config = LogConfig.get_active_config()
            serializer = LogConfigSerializer(config)
            return Response(serializer.data)
        except Exception as e:
            return Response({
                'error': 'Failed to get configuration',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request):
        """Update active logging configuration"""
        try:
            config = LogConfig.get_active_config()

            # Track who updated the configuration
            data = request.data.copy()
            data['updated_by'] = request.user.id

            serializer = LogConfigSerializer(config, data=data, partial=True)
            if serializer.is_valid():
                serializer.save()

                # Clear cache to force reload
                from django.core.cache import cache
                cache.delete('log_config')
                cache.delete('active_log_config')

                # Log configuration change
                from .utils import LoggingUtils
                LoggingUtils.log_user_activity(
                    user=request.user,
                    activity_type='ADMIN_ACTION',
                    action='Updated logging configuration',
                    description=f'Modified logging configuration: {config.name}',
                    request=request,
                    metadata={
                        'config_id': config.id,
                        'changes': list(data.keys())
                    }
                )

                return Response(serializer.data)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': 'Failed to update configuration',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogConfigListView(APIView):
    """Manage multiple logging configurations"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get all logging configurations"""
        try:
            configs = LogConfig.objects.all()
            serializer = LogConfigSerializer(configs, many=True)
            return Response({
                'configurations': serializer.data,
                'active_config_id': LogConfig.get_active_config().id
            })
        except Exception as e:
            return Response({
                'error': 'Failed to get configurations',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """Create new logging configuration"""
        try:
            data = request.data.copy()
            data['created_by'] = request.user.id
            data['updated_by'] = request.user.id

            serializer = LogConfigSerializer(data=data)
            if serializer.is_valid():
                config = serializer.save()

                # Log configuration creation
                from .utils import LoggingUtils
                LoggingUtils.log_user_activity(
                    user=request.user,
                    activity_type='ADMIN_ACTION',
                    action='Created logging configuration',
                    description=f'Created new logging configuration: {config.name}',
                    request=request,
                    metadata={'config_id': config.id}
                )

                return Response(serializer.data, status=status.HTTP_201_CREATED)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': 'Failed to create configuration',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogConfigActivateView(APIView):
    """Activate a specific logging configuration"""
    permission_classes = [IsAdminUser]

    def post(self, request, config_id):
        """Activate a logging configuration"""
        try:
            config = LogConfig.objects.get(id=config_id)

            # Deactivate all other configurations
            LogConfig.objects.filter(is_active=True).update(is_active=False)

            # Activate the selected configuration
            config.is_active = True
            config.updated_by = request.user
            config.save()

            # Clear cache
            from django.core.cache import cache
            cache.delete('log_config')
            cache.delete('active_log_config')

            # Log configuration activation
            from .utils import LoggingUtils
            LoggingUtils.log_user_activity(
                user=request.user,
                activity_type='ADMIN_ACTION',
                action='Activated logging configuration',
                description=f'Activated logging configuration: {config.name}',
                request=request,
                metadata={'config_id': config.id}
            )

            return Response({
                'message': f'Configuration "{config.name}" activated successfully',
                'config': LogConfigSerializer(config).data
            })

        except LogConfig.DoesNotExist:
            return Response({
                'error': 'Configuration not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'Failed to activate configuration',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogConfigTestView(APIView):
    """Test logging configuration"""
    permission_classes = [IsAdminUser]

    def post(self, request):
        """Test current logging configuration"""
        try:
            config = LogConfig.get_active_config()

            # Generate test logs at different levels
            test_results = {}

            from .utils import LoggingUtils

            # Test different log levels
            levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            for level in levels:
                if config.should_log_level(level):
                    LoggingUtils.log_error_manual(
                        error_type='BUSINESS_LOGIC',
                        error_message=f'Test {level} message from configuration test',
                        view_name='log_config_test',
                        severity=level if level in ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'] else 'MEDIUM',
                        user=request.user,
                        request=request,
                        additional_data={'test': True, 'level': level}
                    )
                    test_results[level] = 'Logged'
                else:
                    test_results[level] = 'Filtered out'

            # Test user activity logging
            if config.enable_activity_logging:
                LoggingUtils.log_user_activity(
                    user=request.user,
                    activity_type='ADMIN_ACTION',
                    action='Tested logging configuration',
                    description='Configuration test performed',
                    request=request,
                    metadata={'test': True}
                )
                test_results['user_activity'] = 'Logged'
            else:
                test_results['user_activity'] = 'Disabled'

            return Response({
                'message': 'Logging configuration test completed',
                'config_name': config.name,
                'test_results': test_results,
                'active_features': {
                    'performance_logging': config.enable_performance_logging,
                    'error_logging': config.enable_error_logging,
                    'activity_logging': config.enable_activity_logging,
                    'api_logging': config.enable_api_logging,
                    'db_logging': config.enable_db_logging,
                    'auth_logging': config.enable_auth_logging,
                    'security_logging': config.enable_security_logging,
                    'health_logging': config.enable_health_logging,
                }
            })

        except Exception as e:
            return Response({
                'error': 'Configuration test failed',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LogCleanupView(APIView):
    """Enhanced log cleanup with comprehensive retention policies"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get cleanup statistics and preview"""
        try:
            config = LogConfig.get_active_config()

            # Calculate cutoff dates for different log types
            now = timezone.now()
            general_cutoff = now - timedelta(days=config.log_retention_days)
            error_cutoff = now - timedelta(days=config.error_retention_days)
            security_cutoff = now - timedelta(days=config.security_retention_days)

            # Count logs that would be deleted
            cleanup_preview = {
                'general_logs': {
                    'cutoff_date': general_cutoff,
                    'retention_days': config.log_retention_days,
                    'counts': {
                        'performance_logs': PerformanceLog.objects.filter(created_at__lt=general_cutoff).count(),
                        'user_activities': UserActivity.objects.filter(timestamp__lt=general_cutoff).count(),
                        'api_access_logs': APIAccessLog.objects.filter(timestamp__lt=general_cutoff).count(),
                        'database_query_logs': DatabaseQueryLog.objects.filter(timestamp__lt=general_cutoff).count(),
                        'system_health_logs': SystemHealthLog.objects.filter(timestamp__lt=general_cutoff).count(),
                    }
                },
                'error_logs': {
                    'cutoff_date': error_cutoff,
                    'retention_days': config.error_retention_days,
                    'counts': {
                        'error_logs': ErrorLog.objects.filter(timestamp__lt=error_cutoff).count(),
                    }
                },
                'security_logs': {
                    'cutoff_date': security_cutoff,
                    'retention_days': config.security_retention_days,
                    'counts': {
                        'authentication_logs': AuthenticationLog.objects.filter(timestamp__lt=security_cutoff).count(),
                        'security_incidents': SecurityIncident.objects.filter(timestamp__lt=security_cutoff).count(),
                    }
                }
            }

            # Calculate total counts
            total_to_delete = sum([
                sum(cleanup_preview['general_logs']['counts'].values()),
                sum(cleanup_preview['error_logs']['counts'].values()),
                sum(cleanup_preview['security_logs']['counts'].values())
            ])

            # Get current log counts
            current_counts = {
                'performance_logs': PerformanceLog.objects.count(),
                'error_logs': ErrorLog.objects.count(),
                'user_activities': UserActivity.objects.count(),
                'api_access_logs': APIAccessLog.objects.count(),
                'database_query_logs': DatabaseQueryLog.objects.count(),
                'authentication_logs': AuthenticationLog.objects.count(),
                'security_incidents': SecurityIncident.objects.count(),
                'system_health_logs': SystemHealthLog.objects.count(),
            }

            return Response({
                'config_name': config.name,
                'cleanup_preview': cleanup_preview,
                'total_to_delete': total_to_delete,
                'current_counts': current_counts,
                'total_current': sum(current_counts.values())
            })

        except Exception as e:
            return Response({
                'error': 'Failed to get cleanup preview',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """Execute log cleanup based on retention policies"""
        try:
            config = LogConfig.get_active_config()

            # Get cleanup options from request
            dry_run = request.data.get('dry_run', False)
            archive_before_delete = request.data.get('archive', False)
            specific_types = request.data.get('log_types', None)  # Optional: clean specific types only

            # Calculate cutoff dates
            now = timezone.now()
            general_cutoff = now - timedelta(days=config.log_retention_days)
            error_cutoff = now - timedelta(days=config.error_retention_days)
            security_cutoff = now - timedelta(days=config.security_retention_days)

            cleanup_results = {
                'dry_run': dry_run,
                'archive_created': archive_before_delete,
                'deleted_counts': {},
                'archived_counts': {},
                'errors': []
            }

            # Define log types and their configurations
            log_types_config = {
                'performance_logs': {
                    'model': PerformanceLog,
                    'cutoff': general_cutoff,
                    'date_field': 'created_at'
                },
                'user_activities': {
                    'model': UserActivity,
                    'cutoff': general_cutoff,
                    'date_field': 'timestamp'
                },
                'api_access_logs': {
                    'model': APIAccessLog,
                    'cutoff': general_cutoff,
                    'date_field': 'timestamp'
                },
                'database_query_logs': {
                    'model': DatabaseQueryLog,
                    'cutoff': general_cutoff,
                    'date_field': 'timestamp'
                },
                'system_health_logs': {
                    'model': SystemHealthLog,
                    'cutoff': general_cutoff,
                    'date_field': 'timestamp'
                },
                'error_logs': {
                    'model': ErrorLog,
                    'cutoff': error_cutoff,
                    'date_field': 'timestamp'
                },
                'authentication_logs': {
                    'model': AuthenticationLog,
                    'cutoff': security_cutoff,
                    'date_field': 'timestamp'
                },
                'security_incidents': {
                    'model': SecurityIncident,
                    'cutoff': security_cutoff,
                    'date_field': 'timestamp'
                }
            }

            # Filter to specific types if requested
            if specific_types:
                log_types_config = {k: v for k, v in log_types_config.items() if k in specific_types}

            # Process each log type
            for log_type, config_data in log_types_config.items():
                try:
                    model = config_data['model']
                    cutoff = config_data['cutoff']
                    date_field = config_data['date_field']

                    # Build query
                    filter_kwargs = {f"{date_field}__lt": cutoff}
                    queryset = model.objects.filter(**filter_kwargs)

                    count_to_delete = queryset.count()

                    if count_to_delete > 0:
                        if archive_before_delete and not dry_run:
                            # Archive logs before deletion
                            archived_count = self._archive_logs(log_type, queryset)
                            cleanup_results['archived_counts'][log_type] = archived_count

                        if not dry_run:
                            # Delete the logs
                            deleted_count, _ = queryset.delete()
                            cleanup_results['deleted_counts'][log_type] = deleted_count
                        else:
                            cleanup_results['deleted_counts'][log_type] = count_to_delete
                    else:
                        cleanup_results['deleted_counts'][log_type] = 0

                except Exception as e:
                    cleanup_results['errors'].append(f"Error cleaning {log_type}: {str(e)}")

            # Log the cleanup activity
            if not dry_run:
                from .utils import LoggingUtils
                LoggingUtils.log_user_activity(
                    user=request.user,
                    activity_type='ADMIN_ACTION',
                    action='Log cleanup executed',
                    description=f'Cleaned up logs using configuration: {config.name}',
                    request=request,
                    metadata={
                        'config_id': config.id,
                        'cleanup_results': cleanup_results,
                        'archived': archive_before_delete
                    }
                )

            total_deleted = sum(cleanup_results['deleted_counts'].values())
            total_archived = sum(cleanup_results['archived_counts'].values())

            return Response({
                'message': 'Log cleanup completed successfully' if not dry_run else 'Dry run completed',
                'config_name': config.name,
                'total_deleted': total_deleted,
                'total_archived': total_archived,
                'cleanup_results': cleanup_results
            })

        except Exception as e:
            return Response({
                'error': 'Log cleanup failed',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _archive_logs(self, log_type, queryset):
        """Archive logs to JSON files before deletion"""
        try:
            import json
            import os
            from django.conf import settings
            from django.core import serializers

            # Create archive directory
            archive_dir = os.path.join(settings.BASE_DIR, 'log_archives')
            os.makedirs(archive_dir, exist_ok=True)

            # Create filename with timestamp
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{log_type}_{timestamp}.json"
            filepath = os.path.join(archive_dir, filename)

            # Serialize and save logs
            serialized_data = serializers.serialize('json', queryset)

            with open(filepath, 'w') as f:
                f.write(serialized_data)

            return queryset.count()

        except Exception as e:
            # If archiving fails, log the error but don't stop cleanup
            print(f"Archiving failed for {log_type}: {str(e)}")
            return 0


class LogRetentionPolicyView(APIView):
    """Manage log retention policies"""
    permission_classes = [IsAdminUser]

    def get(self, request):
        """Get current retention policies and statistics"""
        try:
            config = LogConfig.get_active_config()

            # Calculate statistics for each retention period
            now = timezone.now()

            retention_stats = {
                'general_logs': {
                    'retention_days': config.log_retention_days,
                    'cutoff_date': now - timedelta(days=config.log_retention_days),
                    'affected_types': ['performance_logs', 'user_activities', 'api_access_logs', 'database_query_logs', 'system_health_logs']
                },
                'error_logs': {
                    'retention_days': config.error_retention_days,
                    'cutoff_date': now - timedelta(days=config.error_retention_days),
                    'affected_types': ['error_logs']
                },
                'security_logs': {
                    'retention_days': config.security_retention_days,
                    'cutoff_date': now - timedelta(days=config.security_retention_days),
                    'affected_types': ['authentication_logs', 'security_incidents']
                }
            }

            # Get oldest and newest log entries for each type
            log_age_stats = {}

            log_models = {
                'performance_logs': (PerformanceLog, 'created_at'),
                'error_logs': (ErrorLog, 'timestamp'),
                'user_activities': (UserActivity, 'timestamp'),
                'api_access_logs': (APIAccessLog, 'timestamp'),
                'database_query_logs': (DatabaseQueryLog, 'timestamp'),
                'authentication_logs': (AuthenticationLog, 'timestamp'),
                'security_incidents': (SecurityIncident, 'timestamp'),
                'system_health_logs': (SystemHealthLog, 'timestamp'),
            }

            for log_type, (model, date_field) in log_models.items():
                try:
                    oldest = model.objects.order_by(date_field).first()
                    newest = model.objects.order_by(f'-{date_field}').first()
                    total_count = model.objects.count()

                    log_age_stats[log_type] = {
                        'total_count': total_count,
                        'oldest_entry': getattr(oldest, date_field) if oldest else None,
                        'newest_entry': getattr(newest, date_field) if newest else None,
                        'age_days': (now - getattr(oldest, date_field)).days if oldest else 0
                    }
                except Exception:
                    log_age_stats[log_type] = {
                        'total_count': 0,
                        'oldest_entry': None,
                        'newest_entry': None,
                        'age_days': 0
                    }

            return Response({
                'config_name': config.name,
                'retention_policies': retention_stats,
                'log_age_statistics': log_age_stats,
                'recommendations': self._get_retention_recommendations(log_age_stats, retention_stats)
            })

        except Exception as e:
            return Response({
                'error': 'Failed to get retention policy information',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_retention_recommendations(self, log_age_stats, retention_stats):
        """Generate recommendations for retention policy optimization"""
        recommendations = []

        # Check if any log types have very old entries
        for log_type, stats in log_age_stats.items():
            if stats['age_days'] > 365:  # Older than 1 year
                recommendations.append({
                    'type': 'cleanup_needed',
                    'log_type': log_type,
                    'message': f"{log_type} has entries older than 1 year ({stats['age_days']} days). Consider cleanup.",
                    'priority': 'medium'
                })

            if stats['total_count'] > 100000:  # Large number of entries
                recommendations.append({
                    'type': 'large_volume',
                    'log_type': log_type,
                    'message': f"{log_type} has {stats['total_count']:,} entries. Consider more aggressive retention.",
                    'priority': 'low'
                })

        # Check retention policy alignment
        general_retention = retention_stats['general_logs']['retention_days']
        error_retention = retention_stats['error_logs']['retention_days']
        security_retention = retention_stats['security_logs']['retention_days']

        if error_retention <= general_retention:
            recommendations.append({
                'type': 'policy_alignment',
                'message': 'Error log retention should typically be longer than general log retention.',
                'priority': 'medium'
            })

        if security_retention <= error_retention:
            recommendations.append({
                'type': 'policy_alignment',
                'message': 'Security log retention should typically be the longest for compliance.',
                'priority': 'high'
            })

        return recommendations


class LogSearchView(APIView):
    """Advanced log search across all log types"""
    permission_classes = [IsAdminUser]

    def post(self, request):
        """Search across all log types"""
        try:
            query = request.data.get('query', '')
            log_types = request.data.get('log_types', ['all'])
            start_date = request.data.get('start_date')
            end_date = request.data.get('end_date')

            if not query:
                return Response({
                    'error': 'Search query is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            results = {}

            # Parse date filters
            date_filter = Q()
            if start_date:
                start_dt = timezone.datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                date_filter &= Q(timestamp__gte=start_dt) | Q(created_at__gte=start_dt)
            if end_date:
                end_dt = timezone.datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                date_filter &= Q(timestamp__lte=end_dt) | Q(created_at__lte=end_dt)

            # Search in different log types
            if 'all' in log_types or 'errors' in log_types:
                error_results = ErrorLog.objects.filter(
                    Q(error_message__icontains=query) |
                    Q(view_name__icontains=query) |
                    Q(stack_trace__icontains=query)
                )
                if date_filter:
                    error_results = error_results.filter(date_filter)

                results['errors'] = ErrorLogSerializer(
                    error_results[:50], many=True
                ).data

            if 'all' in log_types or 'performance' in log_types:
                perf_results = PerformanceLog.objects.filter(
                    Q(path__icontains=query) |
                    Q(user__username__icontains=query)
                )
                if date_filter:
                    perf_results = perf_results.filter(date_filter)

                results['performance'] = PerformanceLogSerializer(
                    perf_results[:50], many=True
                ).data

            if 'all' in log_types or 'activities' in log_types:
                activity_results = UserActivity.objects.filter(
                    Q(action__icontains=query) |
                    Q(description__icontains=query) |
                    Q(user__username__icontains=query)
                )
                if date_filter:
                    activity_results = activity_results.filter(date_filter)

                results['activities'] = UserActivitySerializer(
                    activity_results[:50], many=True
                ).data

            return Response({
                'query': query,
                'results': results,
                'total_found': sum(len(v) for v in results.values())
            })

        except Exception as e:
            return Response({
                'error': 'Search failed',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
