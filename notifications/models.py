from django.db import models
from django.contrib.auth.models import User


class FCMDevice(models.Model):
    """
    Model to store FCM tokens for users
    """

    DEVICE_TYPES = [("android", "Android"), ("ios", "iOS"), ("web", "Web")]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="fcm_devices",
        null=True,
        blank=True,
    )
    registration_token = models.TextField(unique=True)
    device_type = models.CharField(max_length=20, choices=DEVICE_TYPES)
    interest = models.JSONField(default=list)
    is_registered = models.BooleanField(default=False)
    subscription_type = models.CharField(max_length=50, default="trial", blank=True)
    site = models.CharField(max_length=100, null=True, blank=True)
    registered_courses =  models.JSONField(default=list) 
    # image = models.ImageField(upload_to="device_images/", null=True, blank=True)  
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.device_type} device"

    class Meta:
        # unique_together = ("registration_token")
        verbose_name = "FCM Device"
        verbose_name_plural = "FCM Devices"


class Notification(models.Model):
    """
    Stores notification details separately
    """
    n_title = models.CharField(max_length=255)
    n_body = models.TextField()
    n_image = models.ImageField(upload_to="notifications/", null=True, blank=True)
    n_description = models.TextField(null=True, blank=True)
    d_title = models.CharField(max_length=255)
    d_body = models.TextField()
    d_image = models.ImageField(upload_to="notifications_data/", null=True, blank=True)
    d_description = models.TextField(null=True, blank=True)
    icon = models.CharField(max_length=255, null=True, blank=True)
    d_url = models.URLField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.n_title
    
class NotificationImage(models.Model):
    """
    Model to store notification images
    """
    image = models.ImageField(upload_to="notification_images/")

    def __str__(self):
        return f"Image for {self.notification.n_title}"

