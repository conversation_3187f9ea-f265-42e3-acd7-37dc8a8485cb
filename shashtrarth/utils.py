from django.utils.text import slugify
from unidecode import unidecode
import re
from rest_framework import serializers


def generate_unique_slug(model_class, title, max_length=30):
    """
    Generates a unique slug for the given model_class based on the title.
    Supports Hindi/non-English by converting to Latin using unidecode.
    """
    # Convert to Latin first
    raw_title = unidecode(title)
    slug = slugify(raw_title)

    if len(slug) > max_length:
        slug = slug[:max_length].rstrip('-')

    unique_slug = slug
    count = 1

    while model_class.objects.filter(slug=unique_slug).exists():
        unique_slug = f"{slug[:max_length - len(str(count)) - 1]}-{count}"
        count += 1

    return unique_slug


def validate_username(username):
    """
    Validate username according to the following criteria:
    - Minimum 6 characters
    - Must be alphanumeric (letters and numbers only)
    
    Args:
        username (str): The username to validate
        
    Returns:
        str: The validated username
        
    Raises:
        serializers.ValidationError: If username doesn't meet criteria
    """
    if not username:
        raise serializers.ValidationError("Username is required.")
    
    if len(username) < 6:
        raise serializers.ValidationError("Username must be at least 6 characters long.")
    
    # Check if username contains only alphanumeric characters
    if not re.match(r'^[a-zA-Z0-9]+$', username):
        raise serializers.ValidationError("Username must contain only letters and numbers (alphanumeric characters).")
    
    return username


def validate_password(password):
    """
    Validate password according to the following criteria:
    - Must contain alphabets
    - Must be combined with numbers OR special characters (#, -, _)
    
    Args:
        password (str): The password to validate
        
    Returns:
        str: The validated password
        
    Raises:
        serializers.ValidationError: If password doesn't meet criteria
    """
    if not password:
        raise serializers.ValidationError("Password is required.")
    
    # Check if password contains at least one letter
    if not re.search(r'[a-zA-Z]', password):
        raise serializers.ValidationError("Password must contain at least one letter.")
    
    # Check if password contains at least one number OR one of the allowed special characters
    has_number = re.search(r'[0-9]', password)
    has_special_char = re.search(r'[#\-_]', password)
    
    if not (has_number or has_special_char):
        raise serializers.ValidationError("Password must contain letters combined with numbers or special characters (#, -, _).")
    
    return password
