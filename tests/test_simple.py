"""
Simple tests that don't require database setup to identify basic issues.
"""
from django.test import SimpleTestCase
from django.urls import reverse, NoReverseMatch
from django.contrib.auth.models import User
import json


class URLConfigurationTest(SimpleTestCase):
    """Test URL configuration issues."""
    
    def test_package_detail_url_exists(self):
        """Test if package-detail URL name exists."""
        try:
            url = reverse('package_detail', kwargs={'pk': 1})
            self.assertIsNotNone(url)
        except NoReverseMatch:
            self.fail("URL name 'package_detail' not found")
    
    def test_test_pattern_list_url_exists(self):
        """Test if test-pattern-list URL name exists."""
        try:
            url = reverse('test-pattern-list-create')
            self.assertIsNotNone(url)
        except NoReverseMatch:
            self.fail("URL name 'test-pattern-list-create' not found")
    
    def test_test_pattern_detail_url_exists(self):
        """Test if test-pattern-detail URL name exists."""
        try:
            url = reverse('test-pattern-detail', kwargs={'pk': 1})
            self.assertIsNotNone(url)
        except NoReverseMatch:
            self.fail("URL name 'test-pattern-detail' not found")
    
    def test_banner_list_create_url_exists(self):
        """Test if banner-list-create URL name exists."""
        try:
            url = reverse('banner-list-create')
            self.assertIsNotNone(url)
        except NoReverseMatch:
            self.fail("URL name 'banner-list-create' not found")
    
    def test_subscription_list_url_exists(self):
        """Test if subscription-list URL name exists."""
        try:
            url = reverse('subscription-list')
            self.assertIsNotNone(url)
        except NoReverseMatch:
            self.fail("URL name 'subscription-list' not found")
    
    def test_subscription_search_url_exists(self):
        """Test if subscription-search URL name exists."""
        try:
            url = reverse('subscription-search')
            self.assertIsNotNone(url)
        except NoReverseMatch:
            self.fail("URL name 'subscription-search' not found")
    
    def test_verify_payment_url_exists(self):
        """Test if verify_payment URL name exists."""
        try:
            url = reverse('verify_payment')
            self.assertIsNotNone(url)
        except NoReverseMatch:
            self.fail("URL name 'verify_payment' not found")


class ModelImportTest(SimpleTestCase):
    """Test model imports and basic functionality."""
    
    def test_banner_model_import(self):
        """Test Banner model can be imported."""
        try:
            from contributor.models import Banner
            self.assertTrue(hasattr(Banner, 'banner_name'))
            self.assertTrue(hasattr(Banner, '__str__'))
        except ImportError as e:
            self.fail(f"Failed to import Banner model: {e}")
    
    def test_customrcare_profile_model_import(self):
        """Test CustomrcareProfile model can be imported."""
        try:
            from customrcare.models import CustomrcareProfile
            self.assertTrue(hasattr(CustomrcareProfile, 'contact'))
            self.assertTrue(hasattr(CustomrcareProfile, 'role'))
        except ImportError as e:
            self.fail(f"Failed to import CustomrcareProfile model: {e}")
    
    def test_subcourse_model_import(self):
        """Test SubCourse model can be imported."""
        try:
            from questions.models import SubCourse
            self.assertTrue(hasattr(SubCourse, 'course'))
            self.assertTrue(hasattr(SubCourse, 'name'))
        except ImportError as e:
            self.fail(f"Failed to import SubCourse model: {e}")
    
    def test_test_pattern_model_import(self):
        """Test TestPattern model can be imported."""
        try:
            from paper_engine.models import TestPattern
            self.assertTrue(hasattr(TestPattern, 'sections'))
            self.assertTrue(hasattr(TestPattern, 'name'))
        except ImportError as e:
            self.fail(f"Failed to import TestPattern model: {e}")
    
    def test_practice_record_model_import(self):
        """Test PracticeRecord model can be imported."""
        try:
            from paper_engine.models import PracticeRecord
            self.assertTrue(hasattr(PracticeRecord, 'user'))
            self.assertTrue(hasattr(PracticeRecord, 'questions_practiced'))
        except ImportError as e:
            self.fail(f"Failed to import PracticeRecord model: {e}")
    
    def test_score_tracker_model_import(self):
        """Test ScoreTracker model can be imported."""
        try:
            from paper_engine.models import ScoreTracker
            self.assertTrue(hasattr(ScoreTracker, 'user'))
            self.assertTrue(hasattr(ScoreTracker, 'points'))
        except ImportError as e:
            self.fail(f"Failed to import ScoreTracker model: {e}")


class ViewImportTest(SimpleTestCase):
    """Test view imports and basic functionality."""
    
    def test_page_visitors_view_import(self):
        """Test PageVisitors view can be imported."""
        try:
            from contributor.views import PageVisitors
            self.assertIsNotNone(PageVisitors)
        except ImportError as e:
            self.fail(f"Failed to import PageVisitors view: {e}")
    
    def test_model_counts_view_import(self):
        """Test get_all_model_counts view can be imported."""
        try:
            from contributor.views import get_all_model_counts
            self.assertIsNotNone(get_all_model_counts)
        except ImportError as e:
            self.fail(f"Failed to import get_all_model_counts view: {e}")
    
    def test_banner_views_import(self):
        """Test Banner views can be imported."""
        try:
            from contributor.views import BannerListCreateView, BannerRetrieveUpdateDestroyView
            self.assertIsNotNone(BannerListCreateView)
            self.assertIsNotNone(BannerRetrieveUpdateDestroyView)
        except ImportError as e:
            self.fail(f"Failed to import Banner views: {e}")


class SerializerImportTest(SimpleTestCase):
    """Test serializer imports and basic functionality."""
    
    def test_login_serializer_import(self):
        """Test LoginSerializer can be imported."""
        try:
            from customrcare.serializers import LoginSerializer
            self.assertIsNotNone(LoginSerializer)
        except ImportError as e:
            self.fail(f"Failed to import LoginSerializer: {e}")
    
    def test_customrcare_profile_serializer_import(self):
        """Test CustomrcareProfileSerializer can be imported."""
        try:
            from customrcare.serializers import CustomrcareProfileSerializer
            self.assertIsNotNone(CustomrcareProfileSerializer)
        except ImportError as e:
            self.fail(f"Failed to import CustomrcareProfileSerializer: {e}")


class BasicFunctionalityTest(SimpleTestCase):
    """Test basic functionality without database."""

    def test_json_parsing(self):
        """Test JSON parsing functionality used in PageVisitors."""
        test_data = {'/' : 5, '/about': 3}
        json_str = json.dumps(test_data)
        parsed_data = json.loads(json_str)
        self.assertEqual(parsed_data['/'], 5)
        self.assertEqual(parsed_data['/about'], 3)

    def test_user_model_fields(self):
        """Test User model has expected fields."""
        user_fields = [field.name for field in User._meta.fields]
        self.assertIn('username', user_fields)
        self.assertIn('email', user_fields)
        self.assertIn('first_name', user_fields)
        self.assertIn('last_name', user_fields)

    def test_login_serializer_profile_creation_fields(self):
        """Test LoginSerializer creates profile with valid fields."""
        from customrcare.serializers import LoginSerializer
        from customrcare.models import CustomrcareProfile

        # Check that CustomrcareProfile has the fields used in LoginSerializer
        profile_fields = [field.name for field in CustomrcareProfile._meta.fields]
        self.assertIn('contact', profile_fields)
        self.assertIn('role', profile_fields)
        self.assertIn('user', profile_fields)

        # Check that invalid fields are not used
        self.assertNotIn('security_question', profile_fields)
        self.assertNotIn('security_answer', profile_fields)

    def test_model_counts_api_format(self):
        """Test model counts API logic for naming convention."""
        from django.contrib.auth.models import User
        from django.contrib.contenttypes.models import ContentType

        # Test the naming logic without database queries
        # Simulate what the API does

        # Current implementation: content_type.model (lowercase)
        # Alternative: model_class.__name__ (uppercase)

        # Test with User model as example
        user_model = User
        model_name_lowercase = user_model._meta.model_name  # This gives 'user'
        model_name_uppercase = user_model.__name__  # This gives 'User'

        self.assertEqual(model_name_lowercase, 'user')
        self.assertEqual(model_name_uppercase, 'User')

        # The current API uses content_type.model which is lowercase
        # If tests expect uppercase, we should change the API to use model_class.__name__
