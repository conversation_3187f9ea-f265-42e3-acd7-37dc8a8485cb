from django.utils import timezone
from tokenize import TokenError
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated

from blogs.models import BlogPost
from blogs.serializers import BlogPostSerializer
from .serializers import CustomrcareProfileSerializer, LoginSerializer, TemporaryImageSerializer, SOPSerializer, SOPAdminSerializer, SOPPublicSerializer
from .models import CustomrcareProfile, TemporaryImage, SOP
from .models import Ticket, FrontendError
from .serializers import TicketSerializer, FrontendErrorSerializer
from django.shortcuts import get_object_or_404
from questions.models import MasterOption, MasterQuestion, Question, Option, Subject
from questions.serializers import (
    MasterOptionSerializer,
    MasterQuestionSerializer,
    QuestionSerializer,
    OptionSerializer,
)

from .permissions import IsCustomrcareUser, IsCustomrcareUserNotPost
from django.db.models import Count
from django.db import models
from django.utils.timezone import now, timed<PERSON>ta
from rest_framework.permissions import AllowAny
from contributor.throttles import LoginRateThrottle, RegisterRateThrottle
from rest_framework.decorators import api_view, throttle_classes, permission_classes
import logging
from django.db import transaction

logger = logging.getLogger(__name__)

@throttle_classes([RegisterRateThrottle])
class CustomrcareProfileCreateView(APIView):
    # No permission classes - allow unauthenticated registration

    @transaction.atomic
    def post(self, request):
        serializer = CustomrcareProfileSerializer(data=request.data)
        if serializer.is_valid():
            profile = serializer.save()
            # Generate JWT tokens for immediate login after registration
            refresh = RefreshToken.for_user(profile.user)
            return Response(
                {
                    "profile": serializer.data,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "message": "Profile created successfully",
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        profiles = CustomrcareProfile.objects.all()[::-1]
        serializer = CustomrcareProfileSerializer(profiles, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class CustomrcareProfileRetrieveUpdateDeleteView(APIView):

    # permission_classes = [IsCustomrcareUser]

    def get(self, request, slug):

        profile = get_object_or_404(CustomrcareProfile, slug=slug)
        serializer = CustomrcareProfileSerializer(profile)
        return Response(serializer.data)

    def put(self, request, slug):

        profile = get_object_or_404(CustomrcareProfile, slug=slug)
        serializer = CustomrcareProfileSerializer(
            profile, data=request.data, partial=True
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        profile = get_object_or_404(CustomrcareProfile, slug=slug)
        profile.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
    def patch(self, request, slug):
        profile = get_object_or_404(CustomrcareProfile, slug=slug)
        serializer = CustomrcareProfileSerializer(profile, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@throttle_classes([LoginRateThrottle])
class LoginView(APIView):
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data["user"]
            profile = serializer.validated_data["profile"]
            role = serializer.validated_data["role"]
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "user": {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                    },
                    # "customrcare_id": profile.id,
                    "slug": profile.slug,
                    "role": role,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                },
                status=status.HTTP_200_OK,
            )
        return Response(serializer.errors, status=status.HTTP_401_UNAUTHORIZED)


class LogoutView(APIView):
    permission_classes = [IsCustomrcareUser]

    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response(
                {"message": "Logged out successfully"},
                status=status.HTTP_204_NO_CONTENT,
            )
        except TokenError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.exception("Unexpected error in LogoutView")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TokenRefreshView(APIView):
    def post(self, request):
        refresh_token = request.data.get("refresh")
        if not refresh_token:
            return Response(
                {"error": "Refresh token is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            refresh = RefreshToken(refresh_token)
            return Response(
                {
                    "access": str(refresh.access_token),
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            logger.exception("Unexpected error in TokenRefreshView")
            return Response({"error": str(e)}, status=status.HTTP_401_UNAUTHORIZED)


class TicketListCreateView(APIView):
    permission_classes = [IsCustomrcareUser]
    
    def get(self, request):

        tickets = Ticket.objects.all().order_by("-created_at")
        serializer = TicketSerializer(tickets, many=True)
        return Response(serializer.data)
    
    @transaction.atomic
    def post(self, request):
        with transaction.atomic():
            serializer = TicketSerializer(data=request.data)
            if serializer.is_valid():
                ticket = serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TicketDetailView(APIView):
    permission_classes = [IsCustomrcareUser]

    def get(self, request, slug):
        ticket = get_object_or_404(Ticket, slug=slug)
        serializer = TicketSerializer(ticket)
        return Response(serializer.data)
    
    @transaction.atomic
    def put(self, request, slug):
        ticket = get_object_or_404(Ticket, slug=slug)
        serializer = TicketSerializer(ticket, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):

        ticket = get_object_or_404(Ticket, slug=slug)
        ticket.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class CustomrcareQuestionListView(APIView):
    """
    General questions endpoint for customer care - lists all questions with their options and attachments
    """
    permission_classes = [IsCustomrcareUser]

    def get(self, request, *args, **kwargs):
        try:
            custmorcare = CustomrcareProfile.objects.get(user=request.user)

            # Get all questions
            questions = Question.objects.all()

            # Separate normal questions, master questions, and master options
            normal_questions = questions.filter(
                master_question__isnull=True, master_option__isnull=True
            )
            normal_question_serializer = QuestionSerializer(normal_questions, many=True)

            # Get all master questions
            master_questions = MasterQuestion.objects.all()
            master_question_serializer = MasterQuestionSerializer(master_questions, many=True)

            # Get all master options
            master_options = MasterOption.objects.all()
            master_option_serializer = MasterOptionSerializer(master_options, many=True)

            # Get all blogs
            blogs = BlogPost.objects.all()[::-1]
            blog_serializer = BlogPostSerializer(blogs, many=True)

            response_data = {
                "NormalQuestions": normal_question_serializer.data,
                "MasterQuestions": master_question_serializer.data,
                "MasterOptions": master_option_serializer.data,
                "Blogs": blog_serializer.data,
            }

            return Response(response_data, status=status.HTTP_200_OK)
        except CustomrcareProfile.DoesNotExist:
            return Response(
                {"error": "Customer care profile not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"An error occurred: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CustomrcareQuestionSearchView(APIView):
    permission_classes = [IsCustomrcareUser]

    def get(self, request, *args, **kwargs):
        keyword = request.query_params.get("keyword", "").capitalize()

        subject = Subject.objects.filter(name__iexact=keyword).first()
        # breakpoint()

        custmorcare = CustomrcareProfile.objects.get(user=request.user)
        # breakpoint()

        if subject:
            questions = Question.objects.filter(subject=subject)
        else:
            questions = Question.objects.none()

        normal_questions = questions.filter(
            master_question__isnull=True, master_option__isnull=True
        )
        normal_question_serializer = QuestionSerializer(normal_questions, many=True)

        master_question_ids = (
            questions.filter(master_question__isnull=False)
            .values_list("master_question_id", flat=True)
            .distinct()
        )
        master_questions = MasterQuestion.objects.filter(
            master_question_id__in=master_question_ids
        )
        master_question_serializer = MasterQuestionSerializer(
            master_questions, many=True
        )

        master_option_ids = (
            questions.filter(master_option__isnull=False)
            .values_list("master_option_id", flat=True)
            .distinct()
        )
        master_options = MasterOption.objects.filter(
            master_option_id__in=master_option_ids
        )
        master_option_serializer = MasterOptionSerializer(master_options, many=True)

        # blog_ids = questions.values_list("current_affairs_id", flat=True).distinct()
        # blogs = BlogPost.objects.filter(id__in=blog_ids)
        blogs = BlogPost.objects.all()[::-1]
        blog_serializer = BlogPostSerializer(blogs, many=True)

        response_data = {
            "NormalQuestions": normal_question_serializer.data,
            "MasterQuestions": master_question_serializer.data,
            "MasterOptions": master_option_serializer.data,
            "Blogs": blog_serializer.data,
        }

        return Response(response_data, status=status.HTTP_200_OK)


class CustomrcareQuestionStatusUpdateView(APIView):
    permission_classes = [IsCustomrcareUser]

    def patch(self, request, *args, **kwargs):
        entity_type = request.data.get("entity_type", None)
        entity_id = request.data.get("id", None)
        status_value = request.data.get("approval_status", None)

        if not all([entity_type, entity_id, status_value]):
            return Response(
                {"error": "entity_type, id, and approval_status are required."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        customrcare_profile = CustomrcareProfile.objects.get(user=request.user)

        if not entity_type or not entity_id or not status_value:
            return Response(
                {"error": "entity_type, id, and approval_status are required."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if entity_type == "question":
            try:
                entity = Question.objects.get(pk=entity_id)
            except Question.DoesNotExist:
                return Response(
                    {"error": "Question not found"}, status=status.HTTP_404_NOT_FOUND
                )
        elif entity_type == "master_question":
            try:
                entity = MasterQuestion.objects.get(pk=entity_id)
            except MasterQuestion.DoesNotExist:
                return Response(
                    {"error": "MasterQuestion not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
        elif entity_type == "master_option":
            try:
                entity = MasterOption.objects.get(pk=entity_id)
            except MasterOption.DoesNotExist:
                return Response(
                    {"error": "MasterOption not found"},
                    status=status.HTTP_404_NOT_FOUND,
                )
        elif entity_type == "blog":
            try:
                entity = BlogPost.objects.get(pk=entity_id)
            except BlogPost.DoesNotExist:
                return Response(
                    {"error": "BlogPost not found"}, status=status.HTTP_404_NOT_FOUND
                )
        else:
            return Response(
                {"error": "Invalid entity_type provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )
        
        entity.reason =  request.data.get("reason", None)
        print(entity.reason, request.data.get("reason"))
        if 'reason_document' in request.FILES:
            entity.reason_document = request.FILES['reason_document']   
        entity.approval_status = status_value
        entity.updated_by_custmorcare = customrcare_profile
        entity.updated_at = timezone.now()
        entity.save(
            update_fields=["reason_document", "reason", "approval_status", "updated_by_custmorcare", "updated_at"]
        )

        return Response(
            {"message": f"{entity_type.capitalize()} status updated successfully"},
            status=status.HTTP_200_OK,
        )


class DashboardAPIView(APIView):
    permission_classes = [IsCustomrcareUser]
    
    def get(self, request, *args, **kwargs):
        active_user = request.user
        today = now().date()

        customrcare_profile = CustomrcareProfile.objects.get(user=request.user)

        queryset = Ticket.objects.filter(customer=customrcare_profile)

        start_of_week = today - timedelta(days=today.weekday())
        start_of_month = today.replace(day=1)
        if today.month == 1:
            start_of_previous_month = today.replace(
                month=12, year=today.year - 1, day=1
            )
        else:
            start_of_previous_month = today.replace(month=today.month - 1, day=1)
        end_of_previous_month = start_of_month - timedelta(days=1)

        # Ticket data
        daily_data = queryset.filter(created_at__date=today)
        weekly_data = queryset.filter(created_at__date__gte=start_of_week)
        monthly_data = queryset.filter(
            created_at__year=today.year, created_at__month=today.month
        )
        previous_month_data = queryset.filter(
            created_at__date__gte=start_of_previous_month,
            created_at__date__lte=end_of_previous_month,
        )

        # Question data
        daily_questions = Question.objects.filter(
            updated_at__date=today, updated_by_custmorcare=customrcare_profile
        )
        weekly_questions = Question.objects.filter(
            updated_at__date__gte=start_of_week,
            updated_by_custmorcare=customrcare_profile,
        )
        monthly_questions = Question.objects.filter(
            updated_at__year=today.year,
            updated_at__month=today.month,
            updated_by_custmorcare=customrcare_profile,
        )
        previous_month_questions = Question.objects.filter(
            updated_at__date__gte=start_of_previous_month,
            updated_at__date__lte=end_of_previous_month,
            updated_by_custmorcare=customrcare_profile,
        )

        # Master Question data
        daily_master_questions = MasterQuestion.objects.filter(
            updated_at__date=today, updated_by_custmorcare=customrcare_profile
        )
        weekly_master_questions = MasterQuestion.objects.filter(
            updated_at__date__gte=start_of_week,
            updated_by_custmorcare=customrcare_profile,
        )
        monthly_master_questions = MasterQuestion.objects.filter(
            updated_at__year=today.year,
            updated_at__month=today.month,
            updated_by_custmorcare=customrcare_profile,
        )
        previous_month_master_questions = MasterQuestion.objects.filter(
            updated_at__date__gte=start_of_previous_month,
            updated_at__date__lte=end_of_previous_month,
            updated_by_custmorcare=customrcare_profile,
        )

        # Master Option data
        daily_master_options = MasterOption.objects.filter(
            updated_at__date=today, updated_by_custmorcare=customrcare_profile
        )
        weekly_master_options = MasterOption.objects.filter(
            updated_at__date__gte=start_of_week,
            updated_by_custmorcare=customrcare_profile,
        )
        monthly_master_options = MasterOption.objects.filter(
            updated_at__year=today.year,
            updated_at__month=today.month,
            updated_by_custmorcare=customrcare_profile,
        )
        previous_month_master_options = MasterOption.objects.filter(
            updated_at__date__gte=start_of_previous_month,
            updated_at__date__lte=end_of_previous_month,
            updated_by_custmorcare=customrcare_profile,
        )

        # Blog data
        daily_blogs = BlogPost.objects.filter(
            updated_at__date=today, updated_by_custmorcare=customrcare_profile
        )
        weekly_blogs = BlogPost.objects.filter(
            updated_at__date__gte=start_of_week,
            updated_by_custmorcare=customrcare_profile,
        )
        monthly_blogs = BlogPost.objects.filter(
            updated_at__year=today.year,
            updated_at__month=today.month,
            updated_by_custmorcare=customrcare_profile,
        )
        previous_month_blogs = BlogPost.objects.filter(
            updated_at__date__gte=start_of_previous_month,
            updated_at__date__lte=end_of_previous_month,
            updated_by_custmorcare=customrcare_profile,
        )

        def get_ticket_summary(qs):
            return {
                "total_tickets": qs.count(),
                "tickets_by_day": list(
                    qs.extra(select={"day": "DATE(created_at)"})
                    .values("day")
                    .annotate(count=Count("id"))
                    .order_by("day")
                ),
            }

        def get_question_summary(qs):
            return {
                "total_questions": qs.count(),
                "questions_by_day": list(
                    qs.extra(select={"day": "DATE(updated_at)"})
                    .values("day")
                    .annotate(count=Count("question_id"))
                    .order_by("day")
                ),
            }

        def get_master_question_summary(qs):
            return {
                "total_questions": qs.count(),
                "questions_by_day": list(
                    qs.extra(select={"day": "DATE(created_at)"})
                    .values("day")
                    .annotate(count=Count("master_question_id"))
                    .order_by("day")
                ),
            }

        def get_master_option_summary(qs):
            return {
                "total_master_options": qs.count(),
                "master_options_by_day": list(
                    qs.extra(select={"day": "DATE(created_at)"})
                    .values("day")
                    .annotate(count=Count("master_option_id"))
                    .order_by("day")
                ),
            }

        def get_blog_summary(qs):
            return {
                "total_blogs": qs.count(),
                "blogs_by_day": list(
                    qs.extra(select={"day": "DATE(created_at)"})
                    .values("day")
                    .annotate(count=Count("id"))
                    .order_by("day")
                ),
            }

        data = {
            "active_user": active_user.username,
            "tickets": {
                "daily_tickets": get_ticket_summary(daily_data),
                "weekly_tickets": get_ticket_summary(weekly_data),
                "monthly_tickets": get_ticket_summary(monthly_data),
                "previous_month_tickets": get_ticket_summary(previous_month_data),
            },
            "questions": {
                "daily_questions": get_question_summary(daily_questions),
                "weekly_questions": get_question_summary(weekly_questions),
                "monthly_questions": get_question_summary(monthly_questions),
                "previous_month_questions": get_question_summary(
                    previous_month_questions
                ),
            },
            "master_questions": {
                "daily_master_questions": get_master_question_summary(
                    daily_master_questions
                ),
                "weekly_master_questions": get_master_question_summary(
                    weekly_master_questions
                ),
                "monthly_master_questions": get_master_question_summary(
                    monthly_master_questions
                ),
                "previous_month_master_questions": get_master_question_summary(
                    previous_month_master_questions
                ),
            },
            "master_options": {
                "daily_master_options": get_master_option_summary(daily_master_options),
                "weekly_master_options": get_master_option_summary(
                    weekly_master_options
                ),
                "monthly_master_options": get_master_option_summary(
                    monthly_master_options
                ),
                "previous_month_master_options": get_master_option_summary(
                    previous_month_master_options
                ),
            },
            "blogs": {
                "daily_blogs": get_blog_summary(daily_blogs),
                "weekly_blogs": get_blog_summary(weekly_blogs),
                "monthly_blogs": get_blog_summary(monthly_blogs),
                "previous_month_blogs": get_blog_summary(previous_month_blogs),
            },
        }

        return Response(data)
    
from questions.models import (
    MasterOption,
    MasterQuestion,
    PreviousYearQuestion,
    Question,
)
from contributor.permissions import IsContributorUser
from contributor.models import ContributorPoints, ContributorProfile
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST
class CustomerCareDashboardAPIView(APIView):
    permission_classes = [IsCustomrcareUser]

    def get(self, request):
        try:
            # Get the logged-in contributor
            contributor = CustomrcareProfile.objects.get(user=request.user)

            # Current date and time
            today = now().date()
            month_start = today.replace(day=1)
            week_start = today - timedelta(days=today.weekday())
            previous_month_start = (month_start - timedelta(days=1)).replace(day=1)
            previous_month_end = month_start - timedelta(days=1)
            third_month_start = (previous_month_start - timedelta(days=1)).replace(
                day=1
            )
            third_month_end = previous_month_start - timedelta(days=1)

            # Fetch all related data created by the contributor
            all_questions = Question.objects.all()[::-1]
            master_questions = MasterQuestion.objects.all()[::-1]
            master_options = MasterOption.objects.all()[::-1]
            blogs = BlogPost.objects.all()[::-1]
            previous_questions = PreviousYearQuestion.objects.all()[::-1]
            tickets = Ticket.objects.all()[::-1]

            # Function to get counts for each status
            def get_status_counts(queryset):
                return {
                    "created": queryset.count(),
                    "approved": queryset.filter(approval_status="approved").count(),
                    "pending": queryset.filter(approval_status="pending").count(),
                    "rejected": queryset.filter(approval_status="rejected").count(),
                }
            def get_status_ticket(queryset):
                return {
                    "created": queryset.count(),
                    "open": queryset.filter(ticket_status="open").count(),
                    "closed": queryset.filter(ticket_status="closed").count(),
                    "in-progress": queryset.filter(ticket_status="in-progress").count(),
                }
            # Summaries for all data types
            data_summary = {
                "questions": {
                    "total": get_status_counts(all_questions),
                },
                "master_questions": {
                    "total": get_status_counts(master_questions),
                },
                "master_options": {
                    "total": get_status_counts(master_options),
                },
                "blogs": {
                    "total": get_status_counts(blogs),
                },
                "previous_questions": {
                    "total": get_status_counts(previous_questions),
                },
                "tickets":{
                    "total": get_status_ticket(tickets),
                }
            }

            # Filters for each time period
            def get_period_data(queryset, period_start, period_end=None):
                if period_end:
                    return queryset.filter(
                        created_at__date__gte=period_start,
                        created_at__date__lte=period_end,
                    )
                return queryset.filter(created_at__date__gte=period_start)

            current_month_data = {
                "questions": {
                    "daily": get_status_counts(
                        all_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(all_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(all_questions, month_start)
                    ),
                },
                "master_questions": {
                    "daily": get_status_counts(
                        master_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(master_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(master_questions, month_start)
                    ),
                },
                "blogs": {
                    "daily": get_status_counts(blogs.filter(created_at__date=today)),
                    "weekly": get_status_counts(get_period_data(blogs, week_start)),
                    "monthly": get_status_counts(get_period_data(blogs, month_start)),
                },
                "master_options": {
                    "daily": get_status_counts(
                        master_options.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(master_options, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(master_options, month_start)
                    ),
                },
                "previous_questions": {
                    "daily": get_status_counts(
                        previous_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(previous_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(previous_questions, month_start)
                    ),
                },
                "tickets": {
                    "daily": get_status_ticket(
                        tickets.filter(created_at__date=today)
                    ),
                    "weekly": get_status_ticket(
                        get_period_data(tickets, week_start)
                    ),
                    "monthly": get_status_ticket(
                        get_period_data(tickets, month_start)
                    ),
                },
            }
           
        

            previous_month_data = {
                "questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            all_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
                "master_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
                "blogs": {
                    "monthly": get_status_counts(
                        get_period_data(blogs, previous_month_start, previous_month_end)
                    ),
                },
                "master_options": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_options, previous_month_start, previous_month_end
                        )
                    ),
                },
                "previous_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            previous_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
                "tickets": {
                    "monthly": get_status_ticket(
                        get_period_data(
                            tickets, previous_month_start, previous_month_end
                        )
                    ),
                }
            }
            third_month_data = {
                "questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            all_questions, third_month_start, third_month_end
                        )
                    ),
                },
                "master_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_questions, third_month_start, third_month_end
                        )
                    ),
                },
                "blogs": {
                    "monthly": get_status_counts(
                        get_period_data(blogs, third_month_start, third_month_end)
                    ),
                },
                "master_options": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_options, third_month_start, third_month_end
                        )
                    ),
                },
                "previous_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            previous_questions, third_month_start, third_month_end
                        )
                    ),
                },
                "tickets": {
                    "monthly": get_status_ticket(
                        get_period_data(
                            tickets, previous_month_start, previous_month_end
                        )
                    ),
                }
            }
            # Points calculation helper
            def calculate_points(queryset, multiplier):
                return queryset.count() * multiplier
            # Prepare response
            response_data = {
                "customer_care": contributor.user.username,
                "questions_summary": data_summary,
                "current_month_data": current_month_data,
                "previous_month_data": previous_month_data,
                "third_month_data": third_month_data,
            }
            return Response(response_data, status=HTTP_200_OK)
        except Exception as e:
            logger.exception("Unexpected error in CustomerCareDashboardAPIView")
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=HTTP_400_BAD_REQUEST,
            )

class FrontendErrorView(APIView):
    """Enhanced frontend error logging with comprehensive tracking"""
    permission_classes = [IsCustomrcareUserNotPost]

    def post(self, request):
        """Log frontend errors with comprehensive data"""
        try:
            # Extract error data from request
            data = request.data

            # Validate required fields
            required_fields = ['error_message', 'page_url']
            for field in required_fields:
                if not data.get(field):
                    return Response({
                        'error': f'{field} is required'
                    }, status=status.HTTP_400_BAD_REQUEST)

            # Parse user agent for browser information
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            browser_info = self._parse_user_agent(user_agent)

            # Get client IP
            ip_address = self._get_client_ip(request)

            # Determine error type and severity
            error_type = self._determine_error_type(data.get('error_message', ''), data.get('error_type'))
            severity = self._determine_severity(data.get('severity'), error_type, data.get('error_message', ''))

            # Create comprehensive error data
            error_data = {
                'error_type': error_type,
                'severity': severity,
                'error_message': data.get('error_message', ''),
                'stack_trace': data.get('stack_trace', ''),
                'user': request.user if request.user.is_authenticated else None,
                'session_id': request.session.session_key or '',
                'user_agent': user_agent,
                'ip_address': ip_address,
                'browser_name': browser_info.get('browser', 'OTHER'),
                'browser_version': browser_info.get('version', ''),
                'device_type': browser_info.get('device_type', ''),
                'screen_resolution': data.get('screen_resolution', ''),
                'page_url': data.get('page_url', ''),
                'page_title': data.get('page_title', ''),
                'referrer_url': data.get('referrer_url', ''),
                'component_name': data.get('component_name', ''),
                'function_name': data.get('function_name', ''),
                'line_number': data.get('line_number'),
                'column_number': data.get('column_number'),
                'error_data': data.get('additional_data', {}),
                'user_actions': data.get('user_actions', []),
                'console_logs': data.get('console_logs', []),
            }

            # Save error data
            error_instance = FrontendError.objects.create(**error_data)

            # Also log to the comprehensive logging system
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='FRONTEND',
                error_message=f"Frontend {error_type}: {data.get('error_message', '')}",
                view_name='frontend_error',
                severity=severity,
                user=request.user if request.user.is_authenticated else None,
                request=request,
                additional_data={
                    'frontend_error_id': error_instance.id,
                    'page_url': data.get('page_url', ''),
                    'browser_info': browser_info,
                    'error_context': data.get('additional_data', {})
                }
            )

            return Response({
                "message": "Error logged successfully",
                "id": error_instance.id,
                "error_type": error_type,
                "severity": severity
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            # Fallback error logging
            try:
                FrontendError.objects.create(
                    error_type='UNKNOWN',
                    severity='HIGH',
                    error_message=f"Error logging frontend error: {str(e)}",
                    error_data=request.data,
                    user=request.user if request.user.is_authenticated else None,
                    page_url=request.data.get('page_url', 'unknown'),
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
            except:
                pass  # Prevent infinite error loops

            return Response({
                'error': 'Failed to log error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, request):
        """Get frontend errors with enhanced filtering"""
        try:
            # Get query parameters
            date_filter = request.query_params.get('date')
            error_type = request.query_params.get('error_type')
            severity = request.query_params.get('severity')
            page_url = request.query_params.get('page_url')
            resolved = request.query_params.get('resolved')
            limit = int(request.query_params.get('limit', 50))

            # Build query
            queryset = FrontendError.objects.all()

            if date_filter:
                queryset = queryset.filter(created_at__date=date_filter)
            if error_type:
                queryset = queryset.filter(error_type=error_type)
            if severity:
                queryset = queryset.filter(severity=severity)
            if page_url:
                queryset = queryset.filter(page_url__icontains=page_url)
            if resolved is not None:
                queryset = queryset.filter(resolved=resolved.lower() == 'true')

            # Order and limit
            errors = queryset.order_by('-created_at')[:limit]

            serializer = FrontendErrorSerializer(errors, many=True)
            return Response({
                'errors': serializer.data,
                'total_count': queryset.count(),
                'filters_applied': {
                    'date': date_filter,
                    'error_type': error_type,
                    'severity': severity,
                    'page_url': page_url,
                    'resolved': resolved
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'Failed to retrieve errors',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk=None):
        """Delete frontend error"""
        try:
            error = FrontendError.objects.get(pk=pk)
            error.delete()
            return Response({"message": "Error deleted successfully"}, status=status.HTTP_200_OK)
        except FrontendError.DoesNotExist:
            return Response({"error": "Error not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'Failed to delete error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _parse_user_agent(self, user_agent):
        """Parse user agent string to extract browser information"""
        browser_info = {
            'browser': 'OTHER',
            'version': '',
            'device_type': 'desktop'
        }

        user_agent_lower = user_agent.lower()

        # Detect browser
        if 'chrome' in user_agent_lower and 'edg' not in user_agent_lower:
            browser_info['browser'] = 'CHROME'
        elif 'firefox' in user_agent_lower:
            browser_info['browser'] = 'FIREFOX'
        elif 'safari' in user_agent_lower and 'chrome' not in user_agent_lower:
            browser_info['browser'] = 'SAFARI'
        elif 'edg' in user_agent_lower:
            browser_info['browser'] = 'EDGE'
        elif 'opera' in user_agent_lower or 'opr' in user_agent_lower:
            browser_info['browser'] = 'OPERA'

        # Detect device type
        if any(device in user_agent_lower for device in ['mobile', 'android', 'iphone']):
            browser_info['device_type'] = 'mobile'
        elif any(device in user_agent_lower for device in ['tablet', 'ipad']):
            browser_info['device_type'] = 'tablet'

        return browser_info

    def _get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def _determine_error_type(self, error_message, provided_type=None):
        """Determine error type based on error message and context"""
        if provided_type and provided_type in [choice[0] for choice in FrontendError.ERROR_TYPES]:
            return provided_type

        error_message_lower = error_message.lower()

        if any(keyword in error_message_lower for keyword in ['network', 'fetch', 'xhr', 'ajax']):
            return 'NETWORK'
        elif any(keyword in error_message_lower for keyword in ['auth', 'login', 'token', 'unauthorized']):
            return 'AUTHENTICATION'
        elif any(keyword in error_message_lower for keyword in ['permission', 'forbidden', 'access denied']):
            return 'PERMISSION'
        elif any(keyword in error_message_lower for keyword in ['timeout', 'timed out']):
            return 'TIMEOUT'
        elif any(keyword in error_message_lower for keyword in ['load', 'resource', 'script', 'css']):
            return 'RESOURCE_LOAD'
        elif any(keyword in error_message_lower for keyword in ['api', 'endpoint', 'response']):
            return 'API_ERROR'
        elif any(keyword in error_message_lower for keyword in ['render', 'component', 'react', 'vue']):
            return 'RENDER_ERROR'
        elif any(keyword in error_message_lower for keyword in ['validation', 'invalid', 'required']):
            return 'VALIDATION'
        elif any(keyword in error_message_lower for keyword in ['click', 'submit', 'action']):
            return 'USER_ACTION'
        else:
            return 'JAVASCRIPT'

    def _determine_severity(self, provided_severity, error_type, error_message):
        """Determine error severity based on type and message"""
        if provided_severity and provided_severity in [choice[0] for choice in FrontendError.SEVERITY_LEVELS]:
            return provided_severity

        error_message_lower = error_message.lower()

        # Critical errors
        if any(keyword in error_message_lower for keyword in ['crash', 'fatal', 'critical', 'security']):
            return 'CRITICAL'
        elif error_type in ['AUTHENTICATION', 'PERMISSION']:
            return 'HIGH'
        elif error_type in ['NETWORK', 'API_ERROR', 'TIMEOUT']:
            return 'MEDIUM'
        elif error_type in ['VALIDATION', 'USER_ACTION']:
            return 'LOW'
        else:
            return 'MEDIUM'


class FrontendErrorAnalyticsView(APIView):
    """Analytics for frontend errors"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get frontend error analytics"""
        try:
            days = int(request.query_params.get('days', 7))
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)

            # Base queryset
            errors = FrontendError.objects.filter(
                created_at__gte=start_date,
                created_at__lte=end_date
            )

            # Summary statistics
            total_errors = errors.count()
            unresolved_count = errors.filter(resolved=False).count()
            critical_count = errors.filter(severity='CRITICAL').count()

            # Group by error type
            by_type = list(errors.values('error_type').annotate(
                count=models.Count('id')
            ).order_by('-count'))

            # Group by severity
            by_severity = list(errors.values('severity').annotate(
                count=models.Count('id')
            ).order_by('-count'))

            # Group by browser
            by_browser = list(errors.values('browser_name').annotate(
                count=models.Count('id')
            ).order_by('-count'))

            # Group by page URL (top 10)
            by_page = list(errors.values('page_url').annotate(
                count=models.Count('id')
            ).order_by('-count')[:10])

            # Recent critical errors
            recent_errors = errors.filter(
                severity__in=['CRITICAL', 'HIGH']
            ).order_by('-created_at')[:10]

            # Error trends (daily counts for the period)
            error_trends = []
            for i in range(days):
                day = start_date + timedelta(days=i)
                day_errors = errors.filter(
                    created_at__date=day.date()
                ).count()
                error_trends.append({
                    'date': day.date().isoformat(),
                    'count': day_errors
                })

            analytics_data = {
                'period_days': days,
                'total_errors': total_errors,
                'unresolved_count': unresolved_count,
                'critical_count': critical_count,
                'by_type': by_type,
                'by_severity': by_severity,
                'by_browser': by_browser,
                'by_page': by_page,
                'error_trends': error_trends,
                'recent_critical_errors': FrontendErrorSerializer(recent_errors, many=True).data
            }

            return Response(analytics_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'Failed to generate analytics',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FrontendErrorResolutionView(APIView):
    """Manage frontend error resolution"""
    permission_classes = [IsAuthenticated]

    def post(self, request, pk):
        """Mark frontend error as resolved"""
        try:
            error = FrontendError.objects.get(pk=pk)

            error.resolved = True
            error.resolved_by = request.user
            error.resolved_at = timezone.now()
            error.resolution_notes = request.data.get('resolution_notes', '')
            error.save()

            # Log resolution activity
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_user_activity(
                user=request.user,
                activity_type='ADMIN_ACTION',
                action=f'Resolved frontend error #{error.id}',
                description=f'Marked frontend error as resolved: {error.error_message[:100]}',
                request=request,
                metadata={
                    'error_id': error.id,
                    'error_type': error.error_type,
                    'resolution_notes': error.resolution_notes
                }
            )

            return Response({
                'message': 'Error marked as resolved',
                'error': FrontendErrorSerializer(error).data
            }, status=status.HTTP_200_OK)

        except FrontendError.DoesNotExist:
            return Response({
                'error': 'Error not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'Failed to resolve error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, pk):
        """Unresolve frontend error"""
        try:
            error = FrontendError.objects.get(pk=pk)

            error.resolved = False
            error.resolved_by = None
            error.resolved_at = None
            error.resolution_notes = ''
            error.save()

            return Response({
                'message': 'Error marked as unresolved',
                'error': FrontendErrorSerializer(error).data
            }, status=status.HTTP_200_OK)

        except FrontendError.DoesNotExist:
            return Response({
                'error': 'Error not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': 'Failed to unresolve error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class FrontendErrorBulkActionsView(APIView):
    """Bulk actions for frontend errors"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Perform bulk actions on frontend errors"""
        try:
            action = request.data.get('action')
            error_ids = request.data.get('error_ids', [])

            if not action or not error_ids:
                return Response({
                    'error': 'Action and error_ids are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            errors = FrontendError.objects.filter(id__in=error_ids)

            if action == 'resolve':
                resolution_notes = request.data.get('resolution_notes', '')
                errors.update(
                    resolved=True,
                    resolved_by=request.user,
                    resolved_at=timezone.now(),
                    resolution_notes=resolution_notes
                )
                message = f'Resolved {errors.count()} errors'

            elif action == 'unresolve':
                errors.update(
                    resolved=False,
                    resolved_by=None,
                    resolved_at=None,
                    resolution_notes=''
                )
                message = f'Unresolved {errors.count()} errors'

            elif action == 'delete':
                count = errors.count()
                errors.delete()
                message = f'Deleted {count} errors'

            else:
                return Response({
                    'error': 'Invalid action'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Log bulk action
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_user_activity(
                user=request.user,
                activity_type='ADMIN_ACTION',
                action=f'Bulk {action} frontend errors',
                description=f'Performed bulk {action} on {len(error_ids)} frontend errors',
                request=request,
                metadata={
                    'action': action,
                    'error_count': len(error_ids),
                    'error_ids': error_ids
                }
            )

            return Response({
                'message': message,
                'affected_count': len(error_ids)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'Bulk action failed',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
from rest_framework.decorators import api_view
from rest_framework.response import Response

@api_view(['POST'])
def upload_image(request):
    """Handle image upload and store temporarily."""
    cleanup_expired_images()
    serializer = TemporaryImageSerializer(data=request.data,context={'request': request})
    if serializer.is_valid():
        serializer.save()
        return Response({'message': 'Image uploaded successfully', 'data': serializer.data}, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
def list_images(request):
    """List non-expired images."""
    # Get only non-expired images
    now = timezone.now()
    images = TemporaryImage.objects.filter(expires_at__gt=now)
    serializer = TemporaryImageSerializer(images, many=True, context={'request': request})
    return Response({'images': serializer.data})

def cleanup_expired_images():
    """Delete expired images."""
    now = timezone.now()
    expired_images = TemporaryImage.objects.filter(expires_at__lte=now)
    expired_images.delete()


# Walk-Around Images API Views
from .models import WalkAroundImage
from .serializers import WalkAroundImageSerializer

class WalkAroundImageListCreateView(generics.ListCreateAPIView):
    """
    API view for listing and creating walk-around images.
    Only authenticated customers/admin customercare can access.
    """
    serializer_class = WalkAroundImageSerializer
    permission_classes = [IsCustomrcareUser]

    def get_queryset(self):
        """Return walk-around images for the authenticated user."""
        return WalkAroundImage.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Create a new walk-around image for the authenticated user."""
        serializer.save(user=self.request.user)


class WalkAroundImageDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for retrieving, updating, and deleting specific walk-around images.
    Only authenticated customers/admin customercare can access their own images.
    """
    serializer_class = WalkAroundImageSerializer
    permission_classes = [IsCustomrcareUser]

    def get_queryset(self):
        """Return walk-around images for the authenticated user."""
        return WalkAroundImage.objects.filter(user=self.request.user)

    def destroy(self, request, *args, **kwargs):
        """Delete a walk-around image with confirmation message."""
        instance = self.get_object()
        image_title = instance.title or "Walk-around image"
        image_id = instance.id

        # Perform the deletion
        self.perform_destroy(instance)

        return Response({
            'message': f'Walk-around image "{image_title}" (ID: {image_id}) has been successfully deleted.',
            'deleted_image_id': image_id,
            'deleted_image_title': image_title
        }, status=status.HTTP_200_OK)


class WalkAroundImageStatusUpdateView(generics.UpdateAPIView):
    """
    API view specifically for updating the status of walk-around images.
    Allows changing status between 'active' and 'inactive'.
    """
    serializer_class = WalkAroundImageSerializer
    permission_classes = [IsCustomrcareUser]

    def get_queryset(self):
        """Return walk-around images for the authenticated user."""
        return WalkAroundImage.objects.filter(user=self.request.user)

    def patch(self, request, *args, **kwargs):
        """Update only the status field."""
        instance = self.get_object()
        new_status = request.data.get('status')

        if new_status not in ['active', 'inactive']:
            return Response(
                {'error': 'Status must be either "active" or "inactive"'},
                status=status.HTTP_400_BAD_REQUEST
            )

        instance.status = new_status
        instance.save()

        serializer = self.get_serializer(instance)
        return Response(serializer.data)


@api_view(['GET'])
@permission_classes([IsCustomrcareUser])
def walk_around_images_stats(request):
    """
    Get statistics about user's walk-around images.
    Returns count of active and inactive images.
    """
    user = request.user
    active_count = WalkAroundImage.objects.filter(user=user, status='active').count()
    inactive_count = WalkAroundImage.objects.filter(user=user, status='inactive').count()
    total_count = active_count + inactive_count

    return Response({
        'total_images': total_count,
        'active_images': active_count,
        'inactive_images': inactive_count,
        'max_active_allowed': 5,
        'can_add_more_active': active_count < 5
    })


# ============================================================================
# POPUP BANNER VIEWS FOR CUSTOMER CARE
# ============================================================================

from contributor.models import PopupBanner
from contributor.serializers import PopupBannerCustomerCareSerializer, PopupBannerAdminSerializer
from contributor.permissions import IsCustomerCareForPopupBanner, IsAdminForPopupBanner


class CustomerCarePopupBannerListView(generics.ListAPIView):
    """
    View for customer care to list all popup banners
    """
    serializer_class = PopupBannerCustomerCareSerializer
    permission_classes = [IsCustomerCareForPopupBanner]

    def get_queryset(self):
        """Return all banners, with filtering options"""
        queryset = PopupBanner.objects.all().order_by('-created_at')

        # Filter by approval status
        approval_status = self.request.query_params.get('approval_status')
        if approval_status:
            queryset = queryset.filter(approval_status=approval_status)

        # Filter by content type
        content_type = self.request.query_params.get('content_type')
        if content_type:
            queryset = queryset.filter(content_type=content_type)

        # Filter by creator
        created_by = self.request.query_params.get('created_by')
        if created_by:
            queryset = queryset.filter(created_by__username=created_by)

        return queryset


class CustomerCarePopupBannerDetailView(generics.RetrieveUpdateAPIView):
    """
    View for customer care to retrieve and approve/reject popup banners
    """
    queryset = PopupBanner.objects.all()
    serializer_class = PopupBannerCustomerCareSerializer
    permission_classes = [IsCustomerCareForPopupBanner]


class AdminPopupBannerListView(generics.ListAPIView):
    """
    View for admin to list all popup banners with full details
    """
    queryset = PopupBanner.objects.all().order_by('-created_at')
    serializer_class = PopupBannerAdminSerializer
    permission_classes = [IsAdminForPopupBanner]

    def get_queryset(self):
        """Return all banners with filtering options"""
        queryset = PopupBanner.objects.all().order_by('-created_at')

        # Filter by approval status
        approval_status = self.request.query_params.get('approval_status')
        if approval_status:
            queryset = queryset.filter(approval_status=approval_status)

        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        # Filter by content type
        content_type = self.request.query_params.get('content_type')
        if content_type:
            queryset = queryset.filter(content_type=content_type)

        return queryset


class AdminPopupBannerDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    View for admin to retrieve, update, or delete any popup banner
    """
    queryset = PopupBanner.objects.all()
    serializer_class = PopupBannerAdminSerializer
    permission_classes = [IsAdminForPopupBanner]

    def destroy(self, request, *args, **kwargs):
        """Delete banner with success message (Admin)"""
        instance = self.get_object()
        banner_title = instance.title
        banner_id = instance.id
        created_by = instance.created_by.username

        # Perform the deletion
        self.perform_destroy(instance)

        return Response({
            'success': True,
            'message': f'Banner "{banner_title}" (ID: {banner_id}) created by {created_by} has been successfully deleted by admin.',
            'deleted_banner': {
                'id': banner_id,
                'title': banner_title,
                'created_by': created_by
            },
            'deleted_by': request.user.username
        }, status=status.HTTP_200_OK)


# ============================================================================
# SOP (Standard Operating Procedures) VIEWS
# ============================================================================

class SOPListCreateView(generics.ListCreateAPIView):
    """
    View for listing SOPs (role-based) and creating SOPs (admin only)
    """

    def get_serializer_class(self):
        """Return appropriate serializer based on user role"""
        if self.request.user.is_staff or self.request.user.is_superuser:
            return SOPAdminSerializer
        return SOPPublicSerializer

    def get_queryset(self):
        """Return SOPs based on user role and access level"""
        try:
            user = self.request.user

            if not user.is_authenticated:
                # Anonymous users see no SOPs
                return SOP.objects.none()

            # Admin sees all SOPs
            if user.is_staff or user.is_superuser:
                return SOP.objects.all().order_by('-created')

            # Determine user role
            user_role = None
            if hasattr(user, 'contributor_profile'):
                user_role = 'contributor'
            elif hasattr(user, 'customrcare_profile'):
                user_role = 'customer'

            # Filter SOPs based on access level
            if user_role == 'contributor':
                return SOP.objects.filter(
                    access__in=['contributor', 'all']
                ).order_by('-created')
            elif user_role == 'customer':
                return SOP.objects.filter(
                    access__in=['customer', 'all']
                ).order_by('-created')
            else:
                # Regular users see only 'all' access SOPs
                return SOP.objects.filter(access='all').order_by('-created')

        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='DATABASE',
                error_message=f"Error fetching SOPs: {str(e)}",
                view_name='SOPListCreateView.get_queryset',
                severity='MEDIUM',
                user=self.request.user if self.request.user.is_authenticated else None,
                request=self.request
            )
            return SOP.objects.none()

    def perform_create(self, serializer):
        """Only admin can create SOPs"""
        try:
            if not (self.request.user.is_staff or self.request.user.is_superuser):
                from rest_framework.exceptions import PermissionDenied
                raise PermissionDenied("Only admin users can create SOPs")

            serializer.save(created_by=self.request.user)

        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='VALIDATION',
                error_message=f"Error creating SOP: {str(e)}",
                view_name='SOPListCreateView.perform_create',
                severity='HIGH',
                user=self.request.user,
                request=self.request,
                additional_data={'serializer_data': serializer.validated_data if hasattr(serializer, 'validated_data') else {}}
            )
            raise


class SOPDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    View for retrieving, updating, and deleting SOPs (admin only for CUD operations)
    """

    def get_serializer_class(self):
        """Return appropriate serializer based on user role"""
        if self.request.user.is_staff or self.request.user.is_superuser:
            return SOPAdminSerializer
        return SOPPublicSerializer

    def get_queryset(self):
        """Return SOPs based on user role and access level"""
        try:
            user = self.request.user

            if not user.is_authenticated:
                return SOP.objects.none()

            # Admin sees all SOPs
            if user.is_staff or user.is_superuser:
                return SOP.objects.all()

            # Determine user role for read access
            user_role = None
            if hasattr(user, 'contributor_profile'):
                user_role = 'contributor'
            elif hasattr(user, 'customrcare_profile'):
                user_role = 'customer'

            # Filter SOPs based on access level
            if user_role == 'contributor':
                return SOP.objects.filter(access__in=['contributor', 'all'])
            elif user_role == 'customer':
                return SOP.objects.filter(access__in=['customer', 'all'])
            else:
                return SOP.objects.filter(access='all')

        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='DATABASE',
                error_message=f"Error fetching SOP details: {str(e)}",
                view_name='SOPDetailView.get_queryset',
                severity='MEDIUM',
                user=self.request.user if self.request.user.is_authenticated else None,
                request=self.request
            )
            return SOP.objects.none()

    def perform_update(self, serializer):
        """Only admin can update SOPs"""
        try:
            if not (self.request.user.is_staff or self.request.user.is_superuser):
                from rest_framework.exceptions import PermissionDenied
                raise PermissionDenied("Only admin users can update SOPs")

            serializer.save(updated_by=self.request.user)

        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='VALIDATION',
                error_message=f"Error updating SOP: {str(e)}",
                view_name='SOPDetailView.perform_update',
                severity='HIGH',
                user=self.request.user,
                request=self.request,
                additional_data={'sop_id': self.get_object().id if self.get_object() else None}
            )
            raise

    def perform_destroy(self, instance):
        """Only admin can delete SOPs"""
        try:
            if not (self.request.user.is_staff or self.request.user.is_superuser):
                from rest_framework.exceptions import PermissionDenied
                raise PermissionDenied("Only admin users can delete SOPs")

            instance.delete()

        except Exception as e:
            from log_admin.utils import LoggingUtils
            LoggingUtils.log_error_manual(
                error_type='DATABASE',
                error_message=f"Error deleting SOP: {str(e)}",
                view_name='SOPDetailView.perform_destroy',
                severity='HIGH',
                user=self.request.user,
                request=self.request,
                additional_data={'sop_id': instance.id}
            )
            raise


