from django.urls import path
from .views import (
    CourseWithSubCoursesAPIView,
    PreviousYearQuestionDetailView,
    PreviousYearQuestionListCreateView,
    QuestionListView,
    OptionListView,
    CourseListView,
    CourseDetailView,
    SubCourseListView,
    SubCourseDetailView,
    TierAPIView,
    PaperAPIView,
    SectionAPIView,
    ModuleAPIView,
    SubjectListCreateView,
    SubjectDetailView,
    TopicListCreateView,
    TopicDetailView,
    SubTopicListCreateView,
    SubTopicDetailView,
    MasterQuestionListView,
    MasterQuestionDetailView,
    MasterOptionDetailView,
    MasterOptionListCreateView,
    TestSeries,
    TestSeriesView,
    SubjectRankUpdateView
)

urlpatterns = [
    path("questions/", QuestionListView.as_view()),
    path("questions/<slug:slug>/", QuestionListView.as_view()),
    path("courses/", CourseListView.as_view(), name="course-list"),
    path("courses/<slug:slug>/", CourseDetailView.as_view(), name="course-detail"),
    path(
        "subcourses/",
        SubCourseListView.as_view(),
        name="subcourse-list",
    ),
    path(
        "subcourses/<slug:slug>/",
        SubCourseDetailView.as_view(),
        name="subcourse-detail",
    ),
    path(
        "courses-with-sub-courses/",
        CourseWithSubCoursesAPIView.as_view(),
        name="courses-with-sub-courses",
    ),
    # Tier
    path("tiers/", TierAPIView.as_view(), name="tier-list"),
    path("tiers/<slug:slug>/", TierAPIView.as_view(), name="tier-detail"),
    # Paper
    path("papers/", PaperAPIView.as_view(), name="paper-list"),
    path("papers/<slug:slug>/", PaperAPIView.as_view(), name="paper-detail"),
    # Section
    path("sections/", SectionAPIView.as_view(), name="section-list"),
    path("sections/<slug:slug>/", SectionAPIView.as_view(), name="section-detail"),
    # Module
    path("modules/", ModuleAPIView.as_view(), name="module-list"),
    path("modules/<slug:slug>/", ModuleAPIView.as_view(), name="module-detail"),
    path("subjects/", SubjectListCreateView.as_view(), name="subject-list-create"),
    path("subjects/<slug:slug>/", SubjectDetailView.as_view(), name="subject-detail"),
    path("topics/", TopicListCreateView.as_view(), name="topic-list-create"),
    path("topics/<slug:slug>/", TopicDetailView.as_view(), name="topic-detail"),
    path("subtopics/", SubTopicListCreateView.as_view(), name="subtopic-list-create"),
    path(
        "subtopics/<slug:slug>/", SubTopicDetailView.as_view(), name="subtopic-detail"
    ),
    path("<slug:question_slug>/options/", OptionListView.as_view()),
    path("<slug:question_slug>/options/<slug:slug>/", OptionListView.as_view()),
    path(
        "master-questions/",
        MasterQuestionListView.as_view(),
        name="master-question-list",
    ),
    path(
        "master-questions/<slug:slug>/",
        MasterQuestionDetailView.as_view(),
        name="master-question-detail",
    ),
    path(
        "master-options/",
        MasterOptionListCreateView.as_view(),
        name="master-option-create",
    ),
    path(
        "master-options/<slug:slug>/",
        MasterOptionDetailView.as_view(),
        name="master-option-detail",
    ),
    path(
        "previous-questions/",
        PreviousYearQuestionListCreateView.as_view(),
        name="previous-year-question-list-create",
    ),
    path(
        "previous-questions/<slug:slug>/",
        PreviousYearQuestionDetailView.as_view(),
        name="previous-year-question-detail",
    ),
    path("test-series-card/", TestSeries.as_view(), name="test-series-card"),
    path('test-series/<slug:slug>/', TestSeriesView.as_view(), name='test-series'),
    path('subject-rank/<int:subject_id>/', SubjectRankUpdateView.as_view(), name='subject-rank'),
]
