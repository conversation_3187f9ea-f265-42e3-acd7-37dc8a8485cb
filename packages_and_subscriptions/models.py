from django.db import models, IntegrityError
from django.utils import timezone
from datetime import timedelta, datetime,  timedelta
from django.utils.timezone import now
from students.models import Student
import uuid
import secrets
import string

def generate_random_string(length=16):
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for i in range(length))

class Package(models.Model):
    PACKAGE_TYPE_CHOICES = [
        ('validity', 'Validity Package'),
        ('event', 'Event Package'),
    ]

    name = models.CharField(max_length=225)
    package_type = models.CharField(
        max_length=20,
        choices=PACKAGE_TYPE_CHOICES,
        default='validity',
        help_text="Validity packages provide general access for a time period. Event packages provide access to specific events/competitions."
    )
    description_line_01 = models.CharField(max_length=100, null=True, blank=True)
    description_line_02 = models.CharField(max_length=100, null=True, blank=True)
    description_line_03 = models.CharField(max_length=100, null=True, blank=True)
    description_line_04 = models.CharField(max_length=100, null=True, blank=True)
    description_line_05 = models.CharField(max_length=100, null=True, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    discount_price = models.DecimalField(max_digits=10, decimal_places=2)
    recommended = models.BooleanField(default=False)

    # For validity packages - duration in months
    duration_months = models.IntegerField(
        default=12,
        null=True,
        blank=True,
        help_text="Duration in months for validity packages. Leave blank for event packages."
    )

    # For event packages - specific event details
    event_name = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Name of the specific event/competition for event packages."
    )
    event_start_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Start date of the event for event packages."
    )
    event_end_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="End date of the event for event packages."
    )
    event_description = models.TextField(
        null=True,
        blank=True,
        help_text="Detailed description of the event for event packages."
    )

    # Access control
    restricted_content = models.JSONField(
        default=list,
        help_text="List of content IDs or sections that this package provides access to."
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.get_package_type_display()})"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.package_type == 'validity' and not self.duration_months:
            raise ValidationError("Validity packages must have a duration in months.")
        if self.package_type == 'event' and not self.event_name:
            raise ValidationError("Event packages must have an event name.")
        if self.package_type == 'event' and self.event_start_date and self.event_end_date:
            if self.event_start_date >= self.event_end_date:
                raise ValidationError("Event start date must be before end date.")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    @property
    def is_event_active(self):
        """Check if event package is currently active based on dates"""
        if self.package_type != 'event':
            return True
        if not self.event_start_date or not self.event_end_date:
            return True
        now = timezone.now()
        return self.event_start_date <= now <= self.event_end_date


from django.contrib.auth.models import User
class Subscription(models.Model):
    # user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    package = models.ForeignKey(Package, on_delete=models.CASCADE)
    # subscription_id = models.CharField(max_length=16, unique=True)
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(null=True, blank=True)
    pg_order_id = models.CharField(max_length=100, default="Null")
    pg_payment_id = models.CharField(max_length=100, default="Null")
    pg_signature = models.CharField(max_length=100, default="Null")
    transaction_id = models.CharField(max_length=100, default="Null")
    final_price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    coupon_used = models.CharField(max_length=100, null=True, blank=True)
    referror =  models.CharField(max_length=100, null=True, blank=True)
    gift_card_used = models.CharField(max_length=100, null=True, blank=True)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    is_active = models.BooleanField(default=False)
    expiry_date = models.DateTimeField(null=True, blank=True)


    def save(self, *args, **kwargs):
        if not self.end_date:
            if self.package.package_type == 'validity' and self.package.duration_months:
                # For validity packages, use duration_months
                self.end_date = self.start_date + timedelta(
                    days=self.package.duration_months * 30
                )
            elif self.package.package_type == 'event' and self.package.event_end_date:
                # For event packages, use event end date
                self.end_date = self.package.event_end_date
            else:
                # Default fallback - 1 month
                self.end_date = self.start_date + timedelta(days=30)
        super().save(*args, **kwargs)

    def is_valid_subscription(self):
        """Check if subscription is valid based on package type and dates"""
        if not self.is_active:
            return False

        now = timezone.now()
        if self.end_date and now > self.end_date:
            return False

        # For event packages, also check if event is active
        if self.package.package_type == 'event':
            return self.package.is_event_active

        return True

    def can_access_content(self, content_id=None):
        """Check if this subscription allows access to specific content"""
        if not self.is_valid_subscription():
            return False

        # Validity packages give access to all general content
        if self.package.package_type == 'validity':
            return True

        # Event packages only give access to restricted content
        if self.package.package_type == 'event':
            if content_id is None:
                return True  # General event access
            return content_id in self.package.restricted_content

        return False

    def __str__(self):
        return f"{self.student.user.first_name} {self.student.user.first_name}- {self.package.name}"

class Coupon(models.Model):
    DISCOUNT_TYPE_CHOICES = [
        ('amount', 'Discount Amount'),
        ('percentage', 'Percentage Discount'),
    ]
    discount_type = models.CharField(max_length=100, choices=DISCOUNT_TYPE_CHOICES)
    usage_limit = models.PositiveIntegerField(default=0)
    code = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    discount = models.DecimalField(
        max_digits=5, decimal_places=2, help_text="Discount percentage"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expiry_date = models.DateTimeField()
    overallDiscountAmount = models.DecimalField(max_digits=100, decimal_places=2, default=0) 
    def __str__(self):
        return self.code

    def is_valid(self):
        return (
            self.is_active and
            self.expiry_date > now() and
            (self.usage_limit > 0)
        )
    
def generate_giftcard_code():
    return uuid.uuid4().hex[:16]


class GiftCard(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    code = models.CharField(max_length=16, unique=True, default=generate_giftcard_code)
    balance = models.DecimalField(max_digits=10, decimal_places=2)
    pin = models.CharField(max_length=10)  # or 4-6 digit PIN
    unique_id = models.UUIDField(default=uuid.uuid4, editable=False)

    is_active = models.BooleanField(default=True)
    used = models.BooleanField(default=False)
    redeem_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    def is_valid_combination(self, code_input, pin_input, uid_input):
        return (
            self.code == code_input and
            self.pin == pin_input and
            self.is_active and
            not self.used and
            (not self.expires_at or self.expires_at > timezone.now())
        )

    def __str__(self):
        return self.code
    

class Subscription_Invoice(models.Model):
    subscription = models.ForeignKey('Subscription', on_delete=models.CASCADE, related_name="invoice")
    invoice_number = models.CharField(max_length=20, unique=True)
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    issue_date = models.DateTimeField(default=timezone.now)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    gstin = models.CharField(max_length=15, null=True, blank=True)
    is_paid = models.BooleanField(default=False)

    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.student.user.username}"

    def save(self, *args, **kwargs):
        if not self.invoice_number:
            today = timezone.now().strftime("%Y%m%d")
            count = Subscription_Invoice.objects.filter(issue_date__date=timezone.now().date()).count() + 1
            self.invoice_number = f"INV-{today}-{count:04d}"
        super().save(*args, **kwargs)


class FailedPaymentLog(models.Model):
    user = models.ForeignKey(Student, on_delete=models.SET_NULL, null=True, blank=True)
    order_id = models.CharField(max_length=100)
    payment_id = models.CharField(max_length=100)
    reason = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"FailedPayment [{self.order_id}] at {self.created_at}"
