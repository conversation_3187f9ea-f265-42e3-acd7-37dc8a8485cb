from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied
from django.contrib.auth.models import User
from students.models import Student
from customrcare.models import CustomrcareProfile
from contributor.models import ContributorProfile


class IsStudentOwner(BasePermission):
    """
    Permission class to ensure only students can access their own contact data.
    No other role (admin, customer care, contributor) can access student contacts.
    """
    
    def has_permission(self, request, view):
        """Check if user is authenticated and is a student"""
        if not request.user.is_authenticated:
            raise PermissionDenied(detail="Authentication required.")
        
        # Check if user is a student
        try:
            student = Student.objects.get(user=request.user)
            if student.role == "student":
                return True
            else:
                raise PermissionDenied(
                    detail="Only students can access contact management features."
                )
        except Student.DoesNotExist:
            raise PermissionDenied(
                detail="Student profile not found. Only students can access contact management."
            )
    
    def has_object_permission(self, request, view, obj):
        """Ensure user can only access their own contact data"""
        # For UserContact objects
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        # For ContactRelationship objects
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        # For Contact objects accessed through relationships
        if hasattr(obj, 'user_relationships'):
            return obj.user_relationships.filter(user=request.user).exists()
        
        return False


class IsAdminOnly(BasePermission):
    """
    Permission class that only allows admin users to access student contact data.
    This is for admin views/endpoints that need to see all student contacts.
    """
    
    def has_permission(self, request, view):
        """Check if user is authenticated and is an admin"""
        if not request.user.is_authenticated:
            raise PermissionDenied(detail="Authentication required.")
        
        # Check if user is a superuser/admin
        if request.user.is_superuser or request.user.is_staff:
            return True
        
        raise PermissionDenied(
            detail="Only administrators can access this resource."
        )


class IsStudentOwnerOrAdminReadOnly(BasePermission):
    """
    Permission class that allows:
    - Students to access only their own contact data (full CRUD)
    - Admins to read all contact data (read-only)
    - No access for customer care or contributors
    """
    
    def has_permission(self, request, view):
        """Check basic permission"""
        if not request.user.is_authenticated:
            raise PermissionDenied(detail="Authentication required.")
        
        # Allow admins read-only access
        if (request.user.is_superuser or request.user.is_staff) and request.method in ['GET', 'HEAD', 'OPTIONS']:
            return True
        
        # Check if user is a student for full access
        try:
            student = Student.objects.get(user=request.user)
            if student.role == "student":
                return True
        except Student.DoesNotExist:
            pass
        
        # Explicitly deny access to customer care and contributors
        if hasattr(request.user, 'customrcare_profile') or hasattr(request.user, 'contributor_profile'):
            raise PermissionDenied(
                detail="Customer care and contributor users cannot access contact management."
            )
        
        raise PermissionDenied(
            detail="Only students can access contact management features."
        )
    
    def has_object_permission(self, request, view, obj):
        """Ensure proper object-level permissions"""
        # Admins can read any object
        if (request.user.is_superuser or request.user.is_staff) and request.method in ['GET', 'HEAD', 'OPTIONS']:
            return True
        
        # Students can only access their own data
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False


def get_user_role(user):
    """
    Utility function to determine user role
    Returns: 'admin', 'student', 'customrcare', 'contributor', or 'unknown'
    """
    if not user.is_authenticated:
        return 'unauthenticated'
    
    if user.is_superuser or user.is_staff:
        return 'admin'
    
    # Check for student
    try:
        student = Student.objects.get(user=user)
        return 'student'
    except Student.DoesNotExist:
        pass
    
    # Check for customer care
    try:
        customrcare = CustomrcareProfile.objects.get(user=user)
        return 'customrcare'
    except CustomrcareProfile.DoesNotExist:
        pass
    
    # Check for contributor
    try:
        contributor = ContributorProfile.objects.get(user=user)
        return 'contributor'
    except ContributorProfile.DoesNotExist:
        pass
    
    return 'unknown'


def check_contact_access_permission(user, contact_user=None):
    """
    Utility function to check if a user can access contact data
    
    Args:
        user: The user requesting access
        contact_user: The user whose contacts are being accessed (optional)
    
    Returns:
        dict: Permission information
    """
    user_role = get_user_role(user)
    
    if user_role == 'unauthenticated':
        return {
            'has_access': False,
            'reason': 'not_authenticated',
            'message': 'Authentication required'
        }
    
    if user_role == 'admin':
        return {
            'has_access': True,
            'reason': 'admin_access',
            'message': 'Admin has read access to all contacts',
            'read_only': True
        }
    
    if user_role == 'student':
        if contact_user is None or contact_user == user:
            return {
                'has_access': True,
                'reason': 'owner_access',
                'message': 'Student accessing own contacts',
                'read_only': False
            }
        else:
            return {
                'has_access': False,
                'reason': 'not_owner',
                'message': 'Students can only access their own contacts'
            }
    
    if user_role in ['customrcare', 'contributor']:
        return {
            'has_access': False,
            'reason': 'role_restricted',
            'message': f'{user_role.title()} users cannot access contact management'
        }
    
    return {
        'has_access': False,
        'reason': 'unknown_role',
        'message': 'Unknown user role'
    }
