# Razorpay Dashboard Testing Guide

## 🎯 Objective
Create test transactions that will appear on your Razorpay dashboard so you can verify the payment integration is working correctly.

## 📋 Prerequisites
1. ✅ Django server running on `http://127.0.0.1:8000`
2. ✅ Razorpay test credentials configured
3. ✅ Package purchase API fix applied
4. ✅ At least one student and one paid package in database

## 🚀 Method 1: Using curl Commands

### Step 1: Test Razorpay Configuration
```bash
curl -X GET http://127.0.0.1:8000/api/packages/razorpay-config/
```
**Expected Response:**
```json
{
  "razorpay_key": "rzp_test_lQx7mOhOhSX6FC",
  "razorpay_secret": "VgO0n5Mejir6wlpY2WgAIcsm",
  "currency": "INR",
  "company_name": "My Subscription Platform"
}
```

### Step 2: Create Test Orders (These will appear on dashboard)

**Test Order 1 - Updated Package Name (₹298)**
```bash
curl -X POST http://127.0.0.1:8000/api/packages/v2/create-subscription/ \
  -H "Content-Type: application/json" \
  -d '{"student": 11, "package": 1}'
```

**Test Order 2 - Graiden George Package (₹915)**
```bash
curl -X POST http://127.0.0.1:8000/api/packages/v2/create-subscription/ \
  -H "Content-Type: application/json" \
  -d '{"student": 11, "package": 3}'
```

**Test Order 3 - JEE Mock Test Competition (₹199)**
```bash
curl -X POST http://127.0.0.1:8000/api/packages/v2/create-subscription/ \
  -H "Content-Type: application/json" \
  -d '{"student": 11, "package": 4}'
```

**Test Order 4 - 6 Month Premium Access (₹1499)**
```bash
curl -X POST http://127.0.0.1:8000/api/packages/v2/create-subscription/ \
  -H "Content-Type: application/json" \
  -d '{"student": 11, "package": 5}'
```

### Expected Successful Response:
```json
{
  "success": true,
  "subscription_id": 123,
  "invoice_id": 456,
  "final_price": 298.0,
  "currency": "INR",
  "is_free": false,
  "package_type": "validity",
  "package_name": "Updated Package Name",
  "razorpay_order_id": "order_Qw46TgQY3Xd5z6",
  "razorpay_key": "rzp_test_lQx7mOhOhSX6FC",
  "amount_in_paise": 29800
}
```

## 🚀 Method 2: Using Python Script

Run the provided script:
```bash
python3 create_dashboard_orders.py
```

## 🚀 Method 3: Using Shell Script

Run the provided shell script:
```bash
chmod +x manual_test_orders.sh
./manual_test_orders.sh
```

## 🔍 What to Check on Razorpay Dashboard

### 1. Login to Razorpay Dashboard
- URL: https://dashboard.razorpay.com/
- Use your Razorpay test account credentials

### 2. Navigate to Orders Section
- Go to **Payments** → **Orders**
- Or direct URL: https://dashboard.razorpay.com/app/orders

### 3. Look for Your Test Orders
You should see orders with:
- ✅ **Status**: "created" (waiting for payment)
- ✅ **Amount**: Correct amounts in paise (₹298 = 29800 paise)
- ✅ **Currency**: INR
- ✅ **Recent timestamps**
- ✅ **Order IDs** starting with "order_"

### 4. Check Order Details
Click on any order to see:
- ✅ **Notes section** with student and package information
- ✅ **Amount breakdown**
- ✅ **Creation timestamp**
- ✅ **Order status**

## 🧪 Testing Payment Completion

### Option 1: Using Razorpay Test Cards
1. Click on any "created" order
2. Use "Test Payment" option if available
3. Use these test card details:
   - **Card Number**: 4111 1111 1111 1111
   - **CVV**: Any 3 digits (e.g., 123)
   - **Expiry**: Any future date (e.g., 12/25)
   - **Name**: Any name

### Option 2: Using Payment Verification API
```bash
curl -X POST http://127.0.0.1:8000/api/packages/v2/verify-payment/ \
  -H "Content-Type: application/json" \
  -d '{
    "razorpay_order_id": "order_Qw46TgQY3Xd5z6",
    "razorpay_payment_id": "pay_test123",
    "razorpay_signature": "test_signature",
    "subscription_id": 123
  }'
```

## 📊 Expected Dashboard Data

After running tests, you should see:

### Orders Section:
- 4-5 new orders with "created" status
- Total test amount: ₹2911 (298 + 915 + 199 + 1499)
- All orders in INR currency
- Recent timestamps

### Order Notes Should Include:
```json
{
  "student_id": 11,
  "student_name": "NAYAN DAS",
  "package_id": 1,
  "package_name": "Updated Package Name",
  "package_type": "validity",
  "test_transaction": "true"
}
```

## 🔧 Troubleshooting

### If Orders Don't Appear:
1. **Check Django server logs** for errors
2. **Verify Razorpay credentials** in .env file
3. **Confirm student ID 11 exists** in database
4. **Check package IDs** are correct and active
5. **Ensure internet connection** for Razorpay API calls

### If API Returns Errors:
1. **500 Error**: Check Django server logs
2. **404 Error**: Verify student/package IDs exist
3. **400 Error**: Check request format and required fields

### Common Issues:
- **Student not found**: Use existing student ID
- **Package not found**: Use active package ID
- **Razorpay connection**: Check internet and credentials
- **Email errors**: Should be handled gracefully now

## 🎉 Success Indicators

✅ **Orders visible on Razorpay dashboard**  
✅ **Correct amounts and currency**  
✅ **Student/package details in notes**  
✅ **No API errors during creation**  
✅ **Subscriptions created in Django database**  

## 📞 Next Steps

1. **Verify orders on dashboard**
2. **Test payment completion**
3. **Check subscription activation**
4. **Monitor for any issues**
5. **Deploy to production when ready**

---

**Note**: All test transactions use Razorpay test mode, so no real money is involved. These orders are safe for testing purposes.
