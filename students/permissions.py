from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response
from rest_framework import status
from .models import Student


class IsStudentUser(BasePermission):
    def has_permission(self, request, view):
        user = request.user

        if user.is_authenticated:
            try:
                student = Student.objects.get(user=user)
                if student.role == "student":
                    return True
                raise PermissionDenied(
                    detail="You do not have permission for this resource."
                )
            except Student.DoesNotExist:
                raise PermissionDenied(detail="Student profile not found.")
        raise PermissionDenied(detail="Authentication required.")
