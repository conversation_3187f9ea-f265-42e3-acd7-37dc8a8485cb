from django.core.management.base import BaseCommand
from contacts.models import Contact
from students.models import Student
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Update contact registration status for all contacts'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making any changes to the database',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Starting contact registration status update {"(DRY RUN)" if dry_run else ""}'
            )
        )
        
        # Get all contacts
        contacts = Contact.objects.all()
        total_contacts = contacts.count()
        updated_count = 0
        
        self.stdout.write(f'Checking {total_contacts} contacts for registration status')
        
        for contact in contacts:
            try:
                # Check if this contact number belongs to a registered student
                try:
                    student = Student.objects.get(phone=contact.contact_number)
                    if not contact.is_registered_user or contact.registered_user != student.user:
                        if not dry_run:
                            contact.is_registered_user = True
                            contact.registered_user = student.user
                            contact.save()
                        updated_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'{"Would update" if dry_run else "Updated"} contact: {contact.name} ({contact.contact_number}) -> {student.user.username}'
                            )
                        )
                except Student.DoesNotExist:
                    if contact.is_registered_user:
                        if not dry_run:
                            contact.is_registered_user = False
                            contact.registered_user = None
                            contact.save()
                        updated_count += 1
                        self.stdout.write(
                            self.style.WARNING(
                                f'{"Would mark" if dry_run else "Marked"} contact as unregistered: {contact.name} ({contact.contact_number})'
                            )
                        )
                        
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'Error processing contact {contact.id}: {str(e)}'
                    )
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Completed! {"Would update" if dry_run else "Updated"} {updated_count} contacts.'
            )
        )
        
        if not dry_run:
            logger.info(f'Contact registration status update completed. Updated {updated_count} contacts.')
