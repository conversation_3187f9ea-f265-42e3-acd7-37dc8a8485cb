#!/usr/bin/env python3
"""
Detailed API Test Script for Contributor Earning System
Tests all API endpoints with real data and generates comprehensive Postman documentation.
"""

import os
import sys
import django
import json
from datetime import datetime, timedelta

# Setup Django environment
sys.path.append('/Users/<USER>/Documents/code/shash_b')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from contributor.models import ContributorProfile, ContributorPoints, ContributorEarning


class DetailedAPITester:
    """Detailed API testing with comprehensive documentation generation"""
    
    def __init__(self):
        self.api_client = APIClient()
        self.test_user = None
        self.test_contributor = None
        self.test_results = []
        self.postman_collection = {
            "info": {
                "name": "Contributor Earning System - Complete API Collection",
                "description": "Comprehensive API collection for testing all contributor earning endpoints with real data examples",
                "version": "1.0.0",
                "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "auth": {
                "type": "bearer",
                "bearer": [
                    {
                        "key": "token",
                        "value": "{{auth_token}}",
                        "type": "string"
                    }
                ]
            },
            "event": [
                {
                    "listen": "prerequest",
                    "script": {
                        "type": "text/javascript",
                        "exec": [
                            "// Auto-set authorization header if auth_token is available",
                            "if (pm.variables.get('auth_token')) {",
                            "    pm.request.headers.add({",
                            "        key: 'Authorization',",
                            "        value: 'Bearer ' + pm.variables.get('auth_token')",
                            "    });",
                            "}"
                        ]
                    }
                }
            ],
            "variable": [
                {
                    "key": "base_url",
                    "value": "http://localhost:8000",
                    "type": "string"
                },
                {
                    "key": "auth_token",
                    "value": "your_jwt_token_here",
                    "type": "string"
                }
            ],
            "item": []
        }
    
    def setup_test_user(self):
        """Setup a test user with existing data"""
        print("🔧 Setting up test user...")
        
        # Use existing contributor or create one
        contributors = ContributorProfile.objects.all()
        if contributors.exists():
            self.test_contributor = contributors.first()
            self.test_user = self.test_contributor.user
            print(f"✅ Using existing contributor: {self.test_user.username}")
        else:
            print("❌ No contributors found. Please run the comprehensive test first.")
            return False
        
        return True
    
    def get_jwt_token(self):
        """Get JWT token for the test user"""
        refresh = RefreshToken.for_user(self.test_user)
        return str(refresh.access_token)
    
    def add_postman_request(self, name, method, endpoint, description, query_params=None, body=None, response_example=None):
        """Add a request to the Postman collection"""
        
        url_parts = endpoint.strip('/').split('/')
        request_item = {
            "name": name,
            "request": {
                "method": method,
                "header": [
                    {
                        "key": "Content-Type",
                        "value": "application/json",
                        "type": "text"
                    }
                ],
                "url": {
                    "raw": f"{{{{base_url}}}}/api/contributor{endpoint}",
                    "host": ["{{base_url}}"],
                    "path": ["api", "contributor"] + url_parts
                },
                "description": description
            }
        }
        
        # Add query parameters if provided
        if query_params:
            request_item["request"]["url"]["query"] = [
                {"key": k, "value": v, "description": f"Parameter: {k}"} 
                for k, v in query_params.items()
            ]
        
        # Add request body if provided
        if body:
            request_item["request"]["body"] = {
                "mode": "raw",
                "raw": json.dumps(body, indent=2),
                "options": {
                    "raw": {
                        "language": "json"
                    }
                }
            }
        
        # Add response example if provided
        if response_example:
            request_item["response"] = [{
                "name": "Success Response",
                "originalRequest": request_item["request"].copy(),
                "status": "OK",
                "code": 200,
                "_postman_previewlanguage": "json",
                "header": [
                    {
                        "key": "Content-Type",
                        "value": "application/json"
                    }
                ],
                "body": json.dumps(response_example, indent=2)
            }]
        
        self.postman_collection["item"].append(request_item)
    
    def test_dashboard_api(self):
        """Test dashboard API with real data"""
        print("🔍 Testing Dashboard API...")
        
        token = self.get_jwt_token()
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.api_client.get('/api/contributor/dashboard/')
        
        if response.status_code == 200:
            data = response.json()
            
            self.add_postman_request(
                "Get Contributor Dashboard",
                "GET",
                "/dashboard/",
                "Retrieve comprehensive dashboard information including earnings, activity summary, and points breakdown for the authenticated contributor.",
                response_example=data
            )
            
            print(f"✅ Dashboard API: {data.get('contributor')} - Total Points: {data.get('earnings', {}).get('total_lifetime_earnings', {}).get('total_points', 0)}")
            return True
        else:
            print(f"❌ Dashboard API failed: {response.status_code}")
            return False
    
    def test_earnings_list_api(self):
        """Test earnings list API with filters"""
        print("📋 Testing Earnings List API...")
        
        token = self.get_jwt_token()
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Test basic list
        response = self.api_client.get('/api/contributor/earnings/')
        if response.status_code == 200:
            data = response.json()
            
            self.add_postman_request(
                "Get Earnings List",
                "GET",
                "/earnings/",
                "Retrieve paginated list of all earning records for the authenticated contributor.",
                response_example=data
            )
            
            print(f"✅ Earnings List: {len(data)} records")
        
        # Test with period filter
        response = self.api_client.get('/api/contributor/earnings/?period_type=monthly')
        if response.status_code == 200:
            data = response.json()
            
            self.add_postman_request(
                "Get Monthly Earnings",
                "GET",
                "/earnings/",
                "Retrieve earnings filtered by period type (monthly).",
                query_params={"period_type": "monthly"},
                response_example=data
            )
            
            print(f"✅ Monthly Earnings: {len(data)} records")
        
        # Test with payment status filter
        response = self.api_client.get('/api/contributor/earnings/?is_paid=false')
        if response.status_code == 200:
            data = response.json()
            
            self.add_postman_request(
                "Get Unpaid Earnings",
                "GET",
                "/earnings/",
                "Retrieve earnings filtered by payment status (unpaid).",
                query_params={"is_paid": "false"},
                response_example=data
            )
            
            print(f"✅ Unpaid Earnings: {len(data)} records")
        
        return True

    def run_all_tests(self):
        """Run all API tests and generate documentation"""
        print("🚀 Starting Detailed API Testing...")
        print("=" * 60)

        if not self.setup_test_user():
            return False

        # Run basic tests
        tests = [
            self.test_dashboard_api,
            self.test_earnings_list_api,
        ]

        passed = 0
        failed = 0

        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ {test.__name__} failed: {e}")
                failed += 1

        # Generate documentation
        self.generate_postman_collection()

        print("=" * 60)
        print(f"📊 API Test Summary:")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
        print(f"📄 Generated {len(self.postman_collection['item'])} API endpoints")

        return failed == 0

    def generate_postman_collection(self):
        """Generate comprehensive Postman collection"""
        with open('contributor_earning_detailed_postman_collection.json', 'w') as f:
            json.dump(self.postman_collection, f, indent=2)

        print("✅ Generated: contributor_earning_detailed_postman_collection.json")


def main():
    """Main function"""
    tester = DetailedAPITester()
    success = tester.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
