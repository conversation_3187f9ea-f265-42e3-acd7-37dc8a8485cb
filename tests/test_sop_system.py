"""
Test cases for SOP (Standard Operating Procedures) system
"""

import os
import tempfile
from django.test import TestCase
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from customrcare.models import SOP, CustomrcareProfile
from contributor.models import ContributorProfile


class SOPModelTest(TestCase):
    """Test cases for SOP model"""
    
    def setUp(self):
        """Set up test data"""
        self.admin_user = User.objects.create_superuser(
            username='admin_test',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create a test PDF file
        self.test_pdf = SimpleUploadedFile(
            "test_sop.pdf",
            b"fake pdf content",
            content_type="application/pdf"
        )
    
    def test_sop_creation(self):
        """Test basic SOP creation"""
        sop = SOP.objects.create(
            name="Test SOP",
            pdf=self.test_pdf,
            access="all",
            created_by=self.admin_user
        )
        
        self.assertEqual(sop.name, "Test SOP")
        self.assertEqual(sop.access, "all")
        self.assertEqual(sop.created_by, self.admin_user)
        self.assertIsNotNone(sop.slug)
    
    def test_sop_access_choices(self):
        """Test SOP access level choices"""
        # Test all valid access levels
        for access_level, _ in SOP.ACCESS_CHOICES:
            sop = SOP.objects.create(
                name=f"Test SOP {access_level}",
                pdf=SimpleUploadedFile(f"test_{access_level}.pdf", b"content", content_type="application/pdf"),
                access=access_level,
                created_by=self.admin_user
            )
            self.assertEqual(sop.access, access_level)
    
    def test_sop_string_representation(self):
        """Test SOP string representation"""
        sop = SOP.objects.create(
            name="Test SOP",
            pdf=self.test_pdf,
            access="contributor",
            created_by=self.admin_user
        )
        
        expected_str = "Test SOP (Contributor)"
        self.assertEqual(str(sop), expected_str)


class SOPAPITest(TestCase):
    """Test cases for SOP API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test users
        self.admin_user = User.objects.create_superuser(
            username='admin_test',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.contributor_user = User.objects.create_user(
            username='contributor_test',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.care_user = User.objects.create_user(
            username='care_test',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create profiles
        ContributorProfile.objects.create(user=self.contributor_user)
        CustomrcareProfile.objects.create(user=self.care_user)
        
        # Create test SOPs
        self.sop_all = SOP.objects.create(
            name="SOP for All",
            pdf=SimpleUploadedFile("all.pdf", b"content", content_type="application/pdf"),
            access="all",
            created_by=self.admin_user
        )
        
        self.sop_contributor = SOP.objects.create(
            name="SOP for Contributors",
            pdf=SimpleUploadedFile("contributor.pdf", b"content", content_type="application/pdf"),
            access="contributor",
            created_by=self.admin_user
        )
        
        self.sop_customer = SOP.objects.create(
            name="SOP for Customer Care",
            pdf=SimpleUploadedFile("customer.pdf", b"content", content_type="application/pdf"),
            access="customer",
            created_by=self.admin_user
        )
    
    def test_admin_can_see_all_sops(self):
        """Test admin can see all SOPs"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('sop-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)  # All 3 SOPs
    
    def test_contributor_sees_filtered_sops(self):
        """Test contributor sees only contributor + all access SOPs"""
        self.client.force_authenticate(user=self.contributor_user)
        
        url = reverse('sop-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # contributor + all access SOPs
        
        # Check that the right SOPs are returned
        sop_names = [sop['name'] for sop in response.data]
        self.assertIn("SOP for All", sop_names)
        self.assertIn("SOP for Contributors", sop_names)
        self.assertNotIn("SOP for Customer Care", sop_names)
    
    def test_customer_care_sees_filtered_sops(self):
        """Test customer care sees only customer + all access SOPs"""
        self.client.force_authenticate(user=self.care_user)
        
        url = reverse('sop-list-create')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # customer + all access SOPs
        
        # Check that the right SOPs are returned
        sop_names = [sop['name'] for sop in response.data]
        self.assertIn("SOP for All", sop_names)
        self.assertIn("SOP for Customer Care", sop_names)
        self.assertNotIn("SOP for Contributors", sop_names)
    
    def test_admin_can_create_sop(self):
        """Test admin can create SOP"""
        self.client.force_authenticate(user=self.admin_user)
        
        data = {
            'name': 'New SOP',
            'pdf': SimpleUploadedFile("new.pdf", b"content", content_type="application/pdf"),
            'access': 'all'
        }
        
        url = reverse('sop-list-create')
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New SOP')
        self.assertEqual(response.data['access'], 'all')
    
    def test_non_admin_cannot_create_sop(self):
        """Test non-admin cannot create SOP"""
        self.client.force_authenticate(user=self.contributor_user)
        
        data = {
            'name': 'Unauthorized SOP',
            'pdf': SimpleUploadedFile("unauthorized.pdf", b"content", content_type="application/pdf"),
            'access': 'all'
        }
        
        url = reverse('sop-list-create')
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_admin_can_update_sop(self):
        """Test admin can update SOP"""
        self.client.force_authenticate(user=self.admin_user)
        
        data = {
            'name': 'Updated SOP Name',
            'access': 'contributor'
        }
        
        url = reverse('sop-detail', kwargs={'pk': self.sop_all.pk})
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated SOP Name')
        self.assertEqual(response.data['access'], 'contributor')
    
    def test_non_admin_cannot_update_sop(self):
        """Test non-admin cannot update SOP"""
        self.client.force_authenticate(user=self.contributor_user)
        
        data = {
            'name': 'Unauthorized Update'
        }
        
        url = reverse('sop-detail', kwargs={'pk': self.sop_all.pk})
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_admin_can_delete_sop(self):
        """Test admin can delete SOP"""
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('sop-detail', kwargs={'pk': self.sop_all.pk})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(SOP.objects.filter(pk=self.sop_all.pk).exists())
    
    def test_non_admin_cannot_delete_sop(self):
        """Test non-admin cannot delete SOP"""
        self.client.force_authenticate(user=self.contributor_user)
        
        url = reverse('sop-detail', kwargs={'pk': self.sop_all.pk})
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertTrue(SOP.objects.filter(pk=self.sop_all.pk).exists())
    
    def test_unauthenticated_user_cannot_access_sops(self):
        """Test unauthenticated user cannot access SOPs"""
        url = reverse('sop-list-create')
        response = self.client.get(url)
        
        # Should return empty list or require authentication
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)
    
    def test_sop_detail_access_control(self):
        """Test SOP detail view respects access control"""
        # Contributor should be able to access contributor SOP
        self.client.force_authenticate(user=self.contributor_user)
        url = reverse('sop-detail', kwargs={'pk': self.sop_contributor.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Contributor should NOT be able to access customer-only SOP
        url = reverse('sop-detail', kwargs={'pk': self.sop_customer.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class SOPValidationTest(TestCase):
    """Test cases for SOP validation"""
    
    def setUp(self):
        """Set up test data"""
        self.admin_user = User.objects.create_superuser(
            username='admin_test',
            email='<EMAIL>',
            password='testpass123'
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.admin_user)
    
    def test_pdf_file_validation(self):
        """Test PDF file validation"""
        # Test with non-PDF file
        data = {
            'name': 'Test SOP',
            'pdf': SimpleUploadedFile("test.txt", b"content", content_type="text/plain"),
            'access': 'all'
        }
        
        url = reverse('sop-list-create')
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('pdf', response.data)
    
    def test_large_file_validation(self):
        """Test large file size validation"""
        # Create a file larger than 10MB (simulated)
        large_content = b"x" * (11 * 1024 * 1024)  # 11MB
        
        data = {
            'name': 'Large SOP',
            'pdf': SimpleUploadedFile("large.pdf", large_content, content_type="application/pdf"),
            'access': 'all'
        }
        
        url = reverse('sop-list-create')
        response = self.client.post(url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('pdf', response.data)
