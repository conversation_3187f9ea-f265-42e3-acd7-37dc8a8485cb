# from django.http import JsonResponse
# class RequestValidationMiddleware:
#     def __init__(self, get_response):
#         self.get_response = get_response

#     def __call__(self, request):
#         if request.method in ["POST", "PUT", "PATCH"] and not request.content_type.startswith("application/json"):
#             return JsonResponse({"error": "Only JSO<PERSON> accepted"}, status=400)
#         return self.get_response(request)
