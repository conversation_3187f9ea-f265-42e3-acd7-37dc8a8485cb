from django.urls import path
from .views import BlogPostList, BlogPostDetail, BlogPostListForPublic, BlogPostForPublicDetail, BlogViewCounter


urlpatterns = [
    path("blogposts/", BlogPostList.as_view(), name="blogpost-list"),
    path("blogposts/<slug:slug>/", BlogPostDetail.as_view(), name="blogpost-detail"),
    path("public-blogs/", BlogPostListForPublic.as_view(), name="public-blogs" ),
    path("public-blogs/<slug:slug>/", BlogPostForPublicDetail.as_view() , name="get-public-blog"),
    path("blog_counter/<int:blog_id>/", BlogViewCounter.as_view(), name="blog-view-counter" ),

]
