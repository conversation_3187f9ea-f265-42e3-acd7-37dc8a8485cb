"""
Earning Calculation Service for Contributors
Handles calculation and management of contributor earnings based on their activities.
"""

from decimal import Decimal
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction
from django.db.models import Q, Count
from typing import Dict, Optional, Tuple

from .models import ContributorProfile, ContributorPoints, ContributorEarning
from questions.models import Question, MasterQuestion, MasterOption, PreviousYearQuestion
from blogs.models import BlogPost


class EarningCalculationService:
    """Service class to handle contributor earning calculations"""
    
    @staticmethod
    def get_period_dates(period_type: str, reference_date: datetime = None) -> Tuple[datetime, datetime]:
        """Get start and end dates for a given period type"""
        if reference_date is None:
            reference_date = timezone.now()
        
        if period_type == 'daily':
            start = reference_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end = start + timedelta(days=1) - timedelta(microseconds=1)
        elif period_type == 'weekly':
            days_since_monday = reference_date.weekday()
            start = (reference_date - timedelta(days=days_since_monday)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            end = start + timedelta(days=7) - timedelta(microseconds=1)
        elif period_type == 'monthly':
            start = reference_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if start.month == 12:
                end = start.replace(year=start.year + 1, month=1) - timedelta(microseconds=1)
            else:
                end = start.replace(month=start.month + 1) - timedelta(microseconds=1)
        elif period_type == 'yearly':
            start = reference_date.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            end = start.replace(year=start.year + 1) - timedelta(microseconds=1)
        elif period_type == 'lifetime':
            start = datetime(2020, 1, 1, tzinfo=timezone.get_current_timezone())
            end = timezone.now()
        else:
            raise ValueError(f"Invalid period_type: {period_type}")
        
        return start, end
    
    @staticmethod
    def get_contributor_activity_counts(contributor: ContributorProfile, start_date: datetime, end_date: datetime) -> Dict[str, int]:
        """Get activity counts for a contributor within a date range"""

        # Get counts for each activity type
        normal_questions = Question.objects.filter(
            author=contributor,
            created_at__gte=start_date,
            created_at__lte=end_date
        ).count()

        master_questions = MasterQuestion.objects.filter(
            author=contributor,
            created_at__gte=start_date,
            created_at__lte=end_date
        ).count()

        master_options = MasterOption.objects.filter(
            author=contributor,
            created_at__gte=start_date,
            created_at__lte=end_date
        ).count()

        blogs = BlogPost.objects.filter(
            author=contributor,
            created_at__gte=start_date,
            created_at__lte=end_date
        ).count()

        # Note: PreviousYearQuestion doesn't have an author field, it references a Question
        # We'll count based on the referenced question's author
        previous_questions = PreviousYearQuestion.objects.filter(
            question__author=contributor,
            created_at__gte=start_date,
            created_at__lte=end_date
        ).count()
        
        return {
            'normal_questions': normal_questions,
            'master_questions': master_questions,
            'master_options': master_options,
            'blogs': blogs,
            'previous_questions': previous_questions,
        }
    
    @staticmethod
    def calculate_points_for_activities(activity_counts: Dict[str, int], points_config: ContributorPoints) -> Dict[str, Decimal]:
        """Calculate points earned for each activity type"""
        return {
            'normal_questions_points': Decimal(str(activity_counts['normal_questions'] * points_config.normal_questions)),
            'master_questions_points': Decimal(str(activity_counts['master_questions'] * points_config.master_questions)),
            'master_options_points': Decimal(str(activity_counts['master_options'] * points_config.master_options)),
            'blogs_points': Decimal(str(activity_counts['blogs'] * points_config.blogs)),
            'previous_questions_points': Decimal(str(activity_counts['previous_questions'] * points_config.previous_questions)),
        }
    
    @classmethod
    @transaction.atomic
    def calculate_and_update_earnings(cls, contributor: ContributorProfile, period_type: str = 'monthly', reference_date: datetime = None) -> ContributorEarning:
        """Calculate and update earnings for a contributor for a specific period"""
        
        # Get period dates
        start_date, end_date = cls.get_period_dates(period_type, reference_date)
        
        # Get or create earning record
        earning, created = ContributorEarning.objects.get_or_create(
            contributor=contributor,
            period_type=period_type,
            period_start=start_date,
            period_end=end_date,
            defaults={
                'points_config_used': contributor.get_points_config(),
            }
        )
        
        # Get activity counts
        activity_counts = cls.get_contributor_activity_counts(contributor, start_date, end_date)
        
        # Get points configuration
        points_config = contributor.get_points_config()
        if not points_config:
            raise ValueError("No points configuration found")
        
        # Calculate points
        points_earned = cls.calculate_points_for_activities(activity_counts, points_config)
        
        # Update earning record
        earning.normal_questions_count = activity_counts['normal_questions']
        earning.master_questions_count = activity_counts['master_questions']
        earning.master_options_count = activity_counts['master_options']
        earning.blogs_count = activity_counts['blogs']
        earning.previous_questions_count = activity_counts['previous_questions']
        
        earning.normal_questions_points = points_earned['normal_questions_points']
        earning.master_questions_points = points_earned['master_questions_points']
        earning.master_options_points = points_earned['master_options_points']
        earning.blogs_points = points_earned['blogs_points']
        earning.previous_questions_points = points_earned['previous_questions_points']
        
        earning.points_config_used = points_config
        earning.save()  # This will auto-calculate total_points
        
        return earning
    
    @classmethod
    def get_contributor_total_earnings(cls, contributor: ContributorProfile) -> Dict[str, Decimal]:
        """Get total lifetime earnings for a contributor"""
        earnings = ContributorEarning.objects.filter(contributor=contributor)
        
        total_points = sum(earning.total_points for earning in earnings)
        total_earnings = sum(earning.total_earnings for earning in earnings)
        
        return {
            'total_points': Decimal(str(total_points)),
            'total_earnings': Decimal(str(total_earnings)),
            'earning_records_count': earnings.count(),
        }
    
    @classmethod
    def update_all_contributors_current_month(cls):
        """Update current month earnings for all contributors"""
        contributors = ContributorProfile.objects.all()
        updated_count = 0
        
        for contributor in contributors:
            try:
                cls.calculate_and_update_earnings(contributor, 'monthly')
                updated_count += 1
            except Exception as e:
                print(f"Error updating earnings for {contributor.user.username}: {e}")
        
        return updated_count
    
    @classmethod
    def get_earnings_summary(cls, contributor: ContributorProfile, period_type: str = 'monthly') -> Dict:
        """Get earnings summary for a contributor"""
        start_date, end_date = cls.get_period_dates(period_type)
        
        try:
            earning = ContributorEarning.objects.get(
                contributor=contributor,
                period_type=period_type,
                period_start=start_date,
                period_end=end_date
            )
            
            return {
                'period_type': period_type,
                'period_start': start_date,
                'period_end': end_date,
                'total_points': earning.total_points,
                'total_earnings': earning.total_earnings,
                'activity_breakdown': {
                    'normal_questions': {
                        'count': earning.normal_questions_count,
                        'points': earning.normal_questions_points,
                    },
                    'master_questions': {
                        'count': earning.master_questions_count,
                        'points': earning.master_questions_points,
                    },
                    'master_options': {
                        'count': earning.master_options_count,
                        'points': earning.master_options_points,
                    },
                    'blogs': {
                        'count': earning.blogs_count,
                        'points': earning.blogs_points,
                    },
                    'previous_questions': {
                        'count': earning.previous_questions_count,
                        'points': earning.previous_questions_points,
                    },
                },
                'is_paid': earning.is_paid,
                'paid_at': earning.paid_at,
            }
        except ContributorEarning.DoesNotExist:
            # Calculate on-the-fly if no record exists
            earning = cls.calculate_and_update_earnings(contributor, period_type)
            return cls.get_earnings_summary(contributor, period_type)

    @classmethod
    @transaction.atomic
    def transfer_earnings_to_wallet(cls, earning_id: int) -> Dict:
        """Transfer earnings to contributor's wallet"""
        try:
            earning = ContributorEarning.objects.get(id=earning_id)

            if earning.is_paid:
                return {
                    'success': False,
                    'message': 'Earnings already transferred to wallet'
                }

            # Import wallet models here to avoid circular imports
            from wallet_and_transaction.models import Wallet, Transaction

            # Get or create wallet for the contributor
            wallet, created = Wallet.objects.get_or_create(
                user=earning.contributor.user,
                defaults={'balance': 0}
            )

            # Convert points to wallet balance (1:1 ratio for now)
            points_to_add = int(earning.total_points)

            # Update wallet balance
            wallet.balance += points_to_add
            wallet.save()

            # Create transaction record
            transaction_record = Transaction.objects.create(
                wallet=wallet,
                amount=points_to_add,
                is_credit=True,
                is_debit=False,
                note=f"Earnings transfer - {earning.period_type} period ({earning.period_start.strftime('%Y-%m-%d')} to {earning.period_end.strftime('%Y-%m-%d')})",
                status="Success",
                transaction_id=f"EARN_{earning.id}_{timezone.now().strftime('%Y%m%d%H%M%S')}"
            )

            # Mark earning as paid
            earning.is_paid = True
            earning.paid_at = timezone.now()
            earning.total_earnings = earning.total_points  # Set earnings equal to points for now
            earning.save()

            return {
                'success': True,
                'message': f'Successfully transferred {points_to_add} points to wallet',
                'wallet_balance': wallet.balance,
                'transaction_id': transaction_record.transaction_id
            }

        except ContributorEarning.DoesNotExist:
            return {
                'success': False,
                'message': 'Earning record not found'
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Error transferring earnings: {str(e)}'
            }
