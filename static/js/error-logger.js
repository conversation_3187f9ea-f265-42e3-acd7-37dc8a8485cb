/**
 * Comprehensive Frontend Error Logging System
 * 
 * This library provides comprehensive error logging capabilities for frontend applications.
 * It automatically captures JavaScript errors, network errors, and user actions,
 * then sends them to the backend for analysis.
 * 
 * Usage:
 * 1. Include this script in your HTML
 * 2. Initialize: ErrorLogger.init({ apiEndpoint: '/customrcare/log-error/' })
 * 3. Manual logging: ErrorLogger.logError('Custom error message', { additional: 'data' })
 */

class ErrorLogger {
    constructor() {
        this.config = {
            apiEndpoint: '/customrcare/log-error/',
            maxUserActions: 10,
            maxConsoleMessages: 20,
            enableConsoleCapture: true,
            enableUserActionTracking: true,
            enableNetworkErrorTracking: true,
            enableUnhandledRejectionTracking: true,
            debounceTime: 1000, // ms
            maxRetries: 3,
            retryDelay: 2000, // ms
        };
        
        this.userActions = [];
        this.consoleMessages = [];
        this.errorQueue = [];
        this.isInitialized = false;
        this.sessionId = this.generateSessionId();
        this.lastErrorTime = 0;
        
        // Bind methods to preserve context
        this.handleError = this.handleError.bind(this);
        this.handleUnhandledRejection = this.handleUnhandledRejection.bind(this);
        this.handleNetworkError = this.handleNetworkError.bind(this);
        this.trackUserAction = this.trackUserAction.bind(this);
        this.captureConsoleMessage = this.captureConsoleMessage.bind(this);
    }
    
    /**
     * Initialize the error logger
     * @param {Object} options - Configuration options
     */
    init(options = {}) {
        if (this.isInitialized) {
            console.warn('ErrorLogger is already initialized');
            return;
        }
        
        // Merge configuration
        this.config = { ...this.config, ...options };
        
        // Set up error handlers
        this.setupErrorHandlers();
        
        // Set up user action tracking
        if (this.config.enableUserActionTracking) {
            this.setupUserActionTracking();
        }
        
        // Set up console capture
        if (this.config.enableConsoleCapture) {
            this.setupConsoleCapture();
        }
        
        // Set up network error tracking
        if (this.config.enableNetworkErrorTracking) {
            this.setupNetworkErrorTracking();
        }
        
        this.isInitialized = true;
        console.log('ErrorLogger initialized successfully');
    }
    
    /**
     * Set up global error handlers
     */
    setupErrorHandlers() {
        // JavaScript errors
        window.addEventListener('error', this.handleError);
        
        // Unhandled promise rejections
        if (this.config.enableUnhandledRejectionTracking) {
            window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
        }
    }
    
    /**
     * Set up user action tracking
     */
    setupUserActionTracking() {
        const events = ['click', 'submit', 'change', 'focus', 'blur'];
        
        events.forEach(eventType => {
            document.addEventListener(eventType, (event) => {
                this.trackUserAction(eventType, event);
            }, true);
        });
    }
    
    /**
     * Set up console message capture
     */
    setupConsoleCapture() {
        const originalConsole = { ...console };
        
        ['log', 'warn', 'error', 'info'].forEach(method => {
            console[method] = (...args) => {
                this.captureConsoleMessage(method, args);
                originalConsole[method].apply(console, args);
            };
        });
    }
    
    /**
     * Set up network error tracking
     */
    setupNetworkErrorTracking() {
        // Override fetch
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch.apply(window, args);
                if (!response.ok) {
                    this.handleNetworkError('fetch', args[0], response.status, response.statusText);
                }
                return response;
            } catch (error) {
                this.handleNetworkError('fetch', args[0], 0, error.message);
                throw error;
            }
        };
        
        // Override XMLHttpRequest
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
            const xhr = new originalXHR();
            const originalSend = xhr.send;
            
            xhr.send = function(...args) {
                xhr.addEventListener('error', () => {
                    ErrorLogger.instance.handleNetworkError('xhr', xhr.responseURL || 'unknown', 0, 'Network error');
                });
                
                xhr.addEventListener('load', () => {
                    if (xhr.status >= 400) {
                        ErrorLogger.instance.handleNetworkError('xhr', xhr.responseURL || 'unknown', xhr.status, xhr.statusText);
                    }
                });
                
                return originalSend.apply(xhr, args);
            };
            
            return xhr;
        };
    }
    
    /**
     * Handle JavaScript errors
     */
    handleError(event) {
        const errorData = {
            error_type: 'JAVASCRIPT',
            error_message: event.message || 'Unknown JavaScript error',
            stack_trace: event.error ? event.error.stack : '',
            page_url: window.location.href,
            page_title: document.title,
            referrer_url: document.referrer,
            line_number: event.lineno,
            column_number: event.colno,
            component_name: this.extractComponentName(event.error),
            function_name: this.extractFunctionName(event.error),
            severity: this.determineSeverity(event.message),
            additional_data: {
                filename: event.filename,
                timestamp: new Date().toISOString(),
                user_agent: navigator.userAgent,
                session_id: this.sessionId
            }
        };
        
        this.queueError(errorData);
    }
    
    /**
     * Handle unhandled promise rejections
     */
    handleUnhandledRejection(event) {
        const errorData = {
            error_type: 'JAVASCRIPT',
            error_message: `Unhandled Promise Rejection: ${event.reason}`,
            stack_trace: event.reason && event.reason.stack ? event.reason.stack : '',
            page_url: window.location.href,
            page_title: document.title,
            referrer_url: document.referrer,
            severity: 'HIGH',
            additional_data: {
                promise_rejection: true,
                reason: event.reason,
                timestamp: new Date().toISOString(),
                session_id: this.sessionId
            }
        };
        
        this.queueError(errorData);
    }
    
    /**
     * Handle network errors
     */
    handleNetworkError(type, url, status, statusText) {
        const errorData = {
            error_type: 'NETWORK',
            error_message: `${type.toUpperCase()} Error: ${status} ${statusText}`,
            page_url: window.location.href,
            page_title: document.title,
            severity: status >= 500 ? 'HIGH' : 'MEDIUM',
            additional_data: {
                network_error: true,
                request_type: type,
                request_url: url,
                status_code: status,
                status_text: statusText,
                timestamp: new Date().toISOString(),
                session_id: this.sessionId
            }
        };
        
        this.queueError(errorData);
    }
    
    /**
     * Track user actions
     */
    trackUserAction(eventType, event) {
        const action = {
            type: eventType,
            target: this.getElementSelector(event.target),
            timestamp: new Date().toISOString(),
            page_url: window.location.href
        };
        
        this.userActions.push(action);
        
        // Keep only the last N actions
        if (this.userActions.length > this.config.maxUserActions) {
            this.userActions.shift();
        }
    }
    
    /**
     * Capture console messages
     */
    captureConsoleMessage(level, args) {
        const message = {
            level: level,
            message: args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' '),
            timestamp: new Date().toISOString()
        };
        
        this.consoleMessages.push(message);
        
        // Keep only the last N messages
        if (this.consoleMessages.length > this.config.maxConsoleMessages) {
            this.consoleMessages.shift();
        }
    }
    
    /**
     * Manually log an error
     */
    logError(message, additionalData = {}, severity = 'MEDIUM') {
        const errorData = {
            error_type: 'USER_ACTION',
            error_message: message,
            page_url: window.location.href,
            page_title: document.title,
            referrer_url: document.referrer,
            severity: severity,
            additional_data: {
                manual_log: true,
                ...additionalData,
                timestamp: new Date().toISOString(),
                session_id: this.sessionId
            }
        };
        
        this.queueError(errorData);
    }
    
    /**
     * Queue error for sending (with debouncing)
     */
    queueError(errorData) {
        const now = Date.now();
        
        // Add common data
        errorData.screen_resolution = `${screen.width}x${screen.height}`;
        errorData.user_actions = [...this.userActions];
        errorData.console_logs = [...this.consoleMessages];
        
        this.errorQueue.push(errorData);
        
        // Debounce error sending
        if (now - this.lastErrorTime > this.config.debounceTime) {
            this.lastErrorTime = now;
            setTimeout(() => this.sendQueuedErrors(), 100);
        }
    }
    
    /**
     * Send queued errors to the backend
     */
    async sendQueuedErrors() {
        if (this.errorQueue.length === 0) return;
        
        const errorsToSend = [...this.errorQueue];
        this.errorQueue = [];
        
        for (const errorData of errorsToSend) {
            await this.sendError(errorData);
        }
    }
    
    /**
     * Send individual error to backend with retry logic
     */
    async sendError(errorData, retryCount = 0) {
        try {
            const response = await fetch(this.config.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(errorData)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            console.log('Error logged successfully:', result);
            
        } catch (error) {
            console.error('Failed to log error:', error);
            
            // Retry logic
            if (retryCount < this.config.maxRetries) {
                setTimeout(() => {
                    this.sendError(errorData, retryCount + 1);
                }, this.config.retryDelay * (retryCount + 1));
            }
        }
    }
    
    /**
     * Utility methods
     */
    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    getElementSelector(element) {
        if (!element) return 'unknown';
        
        let selector = element.tagName.toLowerCase();
        if (element.id) selector += `#${element.id}`;
        if (element.className) selector += `.${element.className.split(' ').join('.')}`;
        
        return selector;
    }
    
    extractComponentName(error) {
        if (!error || !error.stack) return '';
        
        const stackLines = error.stack.split('\n');
        for (const line of stackLines) {
            const match = line.match(/at\s+(\w+)/);
            if (match && match[1] !== 'Object') {
                return match[1];
            }
        }
        return '';
    }
    
    extractFunctionName(error) {
        if (!error || !error.stack) return '';
        
        const stackLines = error.stack.split('\n');
        const firstLine = stackLines[1] || stackLines[0];
        const match = firstLine.match(/at\s+(?:Object\.)?(\w+)/);
        
        return match ? match[1] : '';
    }
    
    determineSeverity(message) {
        if (!message) return 'MEDIUM';
        
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('critical') || lowerMessage.includes('fatal')) {
            return 'CRITICAL';
        } else if (lowerMessage.includes('error') || lowerMessage.includes('exception')) {
            return 'HIGH';
        } else if (lowerMessage.includes('warning') || lowerMessage.includes('deprecated')) {
            return 'MEDIUM';
        } else {
            return 'LOW';
        }
    }
}

// Create global instance
ErrorLogger.instance = new ErrorLogger();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorLogger;
}

// Global access
window.ErrorLogger = ErrorLogger.instance;
