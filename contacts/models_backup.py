from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import RegexValidator
from students.models import Student


class UserContact(models.Model):
    """
    Model to store contact details uploaded by users from their phone contacts.
    This represents the raw contact data from the user's phone.
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='uploaded_contacts',
        help_text="The user who uploaded this contact"
    )
    name = models.CharField(
        max_length=255,
        help_text="Contact name as stored in user's phone"
    )
    contact_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )],
        help_text="Contact phone number"
    )
    raw_data = models.JSONField(
        null=True,
        blank=True,
        help_text="Store any additional contact data from phone"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'contact_number']
        indexes = [
            models.Index(fields=['user', 'contact_number']),
            models.Index(fields=['contact_number']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} -> {self.name} ({self.contact_number})"


class Contact(models.Model):
    """
    Model to store unique contacts with active/inactive status.
    This represents the processed contact data.
    """
    name = models.CharField(max_length=255)
    contact_number = models.CharField(
        max_length=15,
        unique=True,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
        )]
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this contact is active in the system"
    )
    is_registered_user = models.BooleanField(
        default=False,
        help_text="Whether this contact number belongs to a registered user"
    )
    registered_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='contact_profile',
        help_text="If this contact is a registered user, reference to that user"
    )
    first_seen = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['contact_number']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_registered_user']),
        ]

    def __str__(self):
        status = "Active" if self.is_active else "Inactive"
        user_info = f" (User: {self.registered_user.username})" if self.registered_user else ""
        return f"{self.name} ({self.contact_number}) - {status}{user_info}"

    def save(self, *args, **kwargs):
        # Check if this contact number belongs to a registered user
        try:
            student = Student.objects.select_related('user').get(phone=self.contact_number)
            self.registered_user = student.user
            self.is_registered_user = True
        except Student.DoesNotExist:
            self.registered_user = None
            self.is_registered_user = False

        super().save(*args, **kwargs)


class ContactRelationship(models.Model):
    """
    Model to track relationships between users based on their contacts.
    This shows who has whom in their contact list.
    """
    RELATIONSHIP_TYPES = [
        ('contact', 'Contact'),
        ('mutual', 'Mutual Contact'),
        ('friend', 'Friend'),
        ('blocked', 'Blocked'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='contact_relationships',
        help_text="The user who has the contact"
    )
    contact = models.ForeignKey(
        Contact,
        on_delete=models.CASCADE,
        related_name='user_relationships',
        help_text="The contact in user's contact list"
    )
    relationship_type = models.CharField(
        max_length=20,
        choices=RELATIONSHIP_TYPES,
        default='contact'
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this relationship is currently active"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'contact']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['contact', 'is_active']),
            models.Index(fields=['relationship_type']),
        ]

    def __str__(self):
        return f"{self.user.username} -> {self.contact.name} ({self.relationship_type})"


class UserContactStats(models.Model):
    """
    Model to store aggregated statistics about user's contacts.
    This helps in quick retrieval of contact counts and analytics.
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='contact_stats'
    )
    total_contacts_uploaded = models.PositiveIntegerField(
        default=0,
        help_text="Total number of contacts uploaded by user"
    )
    active_relationships = models.PositiveIntegerField(
        default=0,
        help_text="Number of active relationships"
    )
    mutual_contacts = models.PositiveIntegerField(
        default=0,
        help_text="Number of mutual contacts (both users have each other)"
    )
    registered_friends = models.PositiveIntegerField(
        default=0,
        help_text="Number of contacts who are registered users"
    )
    last_sync = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Last time contact sync was performed"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Contact Statistics"
        verbose_name_plural = "User Contact Statistics"

    def __str__(self):
        return f"{self.user.username} - {self.total_contacts_uploaded} contacts"

    def update_stats(self):
        """Update the statistics for this user"""
        self.total_contacts_uploaded = self.user.uploaded_contacts.count()
        self.active_relationships = self.user.contact_relationships.filter(is_active=True).count()

        # Count mutual contacts
        user_contacts = set(
            self.user.contact_relationships.filter(is_active=True)
            .values_list('contact__contact_number', flat=True)
        )
        mutual_count = 0
        for contact_num in user_contacts:
            try:
                contact_user = Student.objects.get(phone=contact_num).user
                if contact_user.contact_relationships.filter(
                    contact__contact_number=self.user.student_profile.phone,
                    is_active=True
                ).exists():
                    mutual_count += 1
            except Student.DoesNotExist:
                continue

        self.mutual_contacts = mutual_count
        self.registered_friends = self.user.contact_relationships.filter(
            is_active=True,
            contact__is_registered_user=True
        ).count()
        self.last_sync = timezone.now()
        self.save()


def check_and_process_referrals(contact):
    """
    Check if a contact is a registered user and process referrals.
    This function is called when contacts are updated or created.
    """
    from students.models import Student, Referral

    try:
        # Check if this contact number belongs to a registered student
        referred_student = Student.objects.get(phone=contact.contact_number)

        # Get all users who have this contact in their contact list
        relationships = ContactRelationship.objects.filter(
            contact=contact,
            is_active=True
        ).select_related('user')

        for relationship in relationships:
            try:
                referrer_student = Student.objects.get(user=relationship.user)

                # Check if referral already exists
                existing_referral = Referral.objects.filter(
                    referrer=referrer_student,
                    referred=referred_student
                ).first()

                if not existing_referral:
                    # Create new referral
                    referral = Referral.objects.create(
                        referrer=referrer_student,
                        referred=referred_student,
                        referral_code=referrer_student.referral_code or f"REF{referrer_student.id}"
                    )

                    # Update referrer's count
                    referrer_student.refferred_count += 1
                    referrer_student.save()

                    # Log the referral
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(f"New referral created: {referrer_student.user.username} referred {referred_student.user.username}")

            except Student.DoesNotExist:
                # The user who has this contact is not a student, skip
                continue

    except Student.DoesNotExist:
        # Contact is not a registered user, nothing to do
        pass
