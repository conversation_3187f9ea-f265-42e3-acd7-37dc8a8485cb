# 🎯 Popup Banner Approval & SOP System Documentation

## 📋 Table of Contents
1. [Popup Banner Approval System](#popup-banner-approval-system)
2. [SOP (Standard Operating Procedures) System](#sop-system)
3. [API Endpoints](#api-endpoints)
4. [Testing Guide](#testing-guide)

---

## 🔄 Popup Banner Approval System

### **Overview**
The popup banner approval system provides a multi-level workflow where contributors create banners, customer care approves them, and admin has override capabilities.

### **Workflow**
```
[Contributor Creates] → [Pending]
         ↓
[Customer Care Reviews] → [Approved by Care] OR [Rejected by Care]
         ↓
[Admin Reviews] → [Approved by Admin] OR [Rejected by Admin]
         ↓
[Activation] → [Active] (visible to public)
```

### **Customer Care Approval API**

#### **List All Banners for Review**
```bash
GET /api/customrcare/popup-banners/

# List all banners
curl -X GET "http://localhost:8000/api/customrcare/popup-banners/" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN"

# Filter by approval status
curl -X GET "http://localhost:8000/api/customrcare/popup-banners/?approval_status=pending" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN"

# Filter by content type
curl -X GET "http://localhost:8000/api/customrcare/popup-banners/?content_type=page_target" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN"

# Filter by creator
curl -X GET "http://localhost:8000/api/customrcare/popup-banners/?created_by=username" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN"
```

#### **Approve Banner**
```bash
PATCH /api/customrcare/popup-banners/{id}/

curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approval_status": "approved_by_care"
  }'
```

#### **Reject Banner**
```bash
PATCH /api/customrcare/popup-banners/{id}/

curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approval_status": "rejected_by_care",
    "rejection_reason": "Content does not meet guidelines"
  }'
```

#### **Activate Banner**
```bash
PATCH /api/customrcare/popup-banners/{id}/

curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_active": true
  }'
```

---

## 📚 SOP (Standard Operating Procedures) System

### **Overview**
The SOP system allows administrators to upload and manage Standard Operating Procedures with role-based access control.

### **Features**
- ✅ **Role-based Access**: Contributors, Customer Care, or All Users
- ✅ **PDF Upload**: Only PDF files allowed (max 10MB)
- ✅ **Admin Only Management**: Only admins can create, update, or delete SOPs
- ✅ **Comprehensive Logging**: All operations are logged for audit trails

### **Access Levels**
| Access Level | Who Can View |
|-------------|-------------|
| `contributor` | Contributors + Admins |
| `customer` | Customer Care + Admins |
| `all` | All authenticated users + Admins |

### **SOP Model Fields**
- **name**: Name of the SOP document
- **pdf**: PDF file upload
- **access**: Access level (contributor/customer/all)
- **created**: Creation timestamp
- **last_update**: Last update timestamp
- **created_by**: Admin who created the SOP
- **updated_by**: Admin who last updated the SOP

---

## 🔗 API Endpoints

### **SOP Endpoints**

#### **List SOPs (Role-based)**
```bash
GET /api/customrcare/sops/

# Contributors see contributor + all access SOPs
curl -X GET "http://localhost:8000/api/customrcare/sops/" \
  -H "Authorization: Bearer CONTRIBUTOR_TOKEN"

# Customer care sees customer + all access SOPs
curl -X GET "http://localhost:8000/api/customrcare/sops/" \
  -H "Authorization: Bearer CARE_TOKEN"

# Admin sees all SOPs
curl -X GET "http://localhost:8000/api/customrcare/sops/" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

#### **Create SOP (Admin Only)**
```bash
POST /api/customrcare/sops/

curl -X POST "http://localhost:8000/api/customrcare/sops/" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -F "name=User Registration Guide" \
  -F "pdf=@/path/to/sop.pdf" \
  -F "access=all"
```

#### **Get SOP Details**
```bash
GET /api/customrcare/sops/{id}/

curl -X GET "http://localhost:8000/api/customrcare/sops/1/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### **Update SOP (Admin Only)**
```bash
PATCH /api/customrcare/sops/{id}/

curl -X PATCH "http://localhost:8000/api/customrcare/sops/1/" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -F "name=Updated SOP Name" \
  -F "access=contributor"
```

#### **Delete SOP (Admin Only)**
```bash
DELETE /api/customrcare/sops/{id}/

curl -X DELETE "http://localhost:8000/api/customrcare/sops/1/" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### **Response Examples**

#### **SOP List Response**
```json
[
  {
    "id": 1,
    "name": "User Registration Guide",
    "pdf": "/media/sop_documents/registration_guide.pdf",
    "access": "all",
    "access_display": "All Users",
    "created": "2025-07-23T20:30:00Z",
    "last_update": "2025-07-23T20:30:00Z",
    "created_by": 1,
    "created_by_username": "admin",
    "updated_by": null,
    "updated_by_username": null,
    "slug": "user-registration-guide"
  }
]
```

#### **SOP Detail Response**
```json
{
  "id": 1,
  "name": "User Registration Guide",
  "pdf": "/media/sop_documents/registration_guide.pdf",
  "access": "all",
  "access_display": "All Users",
  "created": "2025-07-23T20:30:00Z",
  "last_update": "2025-07-23T20:30:00Z",
  "created_by": 1,
  "created_by_username": "admin",
  "updated_by": null,
  "updated_by_username": null,
  "slug": "user-registration-guide"
}
```

---

## 🧪 Testing Guide

### **Popup Banner Approval Testing**

#### **1. Complete Workflow Test**
```bash
# Step 1: Contributor creates banner
curl -X POST "http://localhost:8000/api/contributor/popup-banners/" \
  -H "Authorization: Bearer CONTRIBUTOR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Workflow Banner",
    "content_type": "page_target",
    "page_target": "/home",
    "delay_ms": 2000,
    "priority": "medium"
  }'

# Step 2: Customer care approves
curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"approval_status": "approved_by_care"}'

# Step 3: Customer care activates
curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"is_active": true}'

# Step 4: Verify public visibility
curl -X GET "http://localhost:8000/api/popup-banners/"
```

### **SOP System Testing**

#### **1. Admin Creates SOP**
```bash
curl -X POST "http://localhost:8000/api/customrcare/sops/" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -F "name=Contributor Guidelines" \
  -F "pdf=@/path/to/guidelines.pdf" \
  -F "access=contributor"
```

#### **2. Role-based Access Test**
```bash
# Contributor should see contributor + all access SOPs
curl -X GET "http://localhost:8000/api/customrcare/sops/" \
  -H "Authorization: Bearer CONTRIBUTOR_TOKEN"

# Customer care should see customer + all access SOPs
curl -X GET "http://localhost:8000/api/customrcare/sops/" \
  -H "Authorization: Bearer CARE_TOKEN"
```

#### **3. Permission Test**
```bash
# Non-admin should get permission denied
curl -X POST "http://localhost:8000/api/customrcare/sops/" \
  -H "Authorization: Bearer CONTRIBUTOR_TOKEN" \
  -F "name=Unauthorized SOP" \
  -F "pdf=@/path/to/sop.pdf"
# Expected: 403 Forbidden
```

---

## ✅ **Implementation Status**

### **Popup Banner Approval System** ✅
- [x] Customer care list/filter banners
- [x] Customer care approve/reject banners
- [x] Customer care activate banners
- [x] Admin override capabilities
- [x] Comprehensive error logging
- [x] API documentation complete

### **SOP System** ✅
- [x] SOP model with role-based access
- [x] PDF file upload with validation
- [x] Admin-only CRUD operations
- [x] Role-based viewing permissions
- [x] Comprehensive error logging
- [x] Admin interface integration
- [x] API endpoints complete

Both systems are production-ready with comprehensive error handling and logging! 🚀
