#!/usr/bin/env python3
"""
Test script to verify image attachment functionality in questions, options, and explanations.
"""

import os
import sys
import django
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Override ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

from django.contrib.auth.models import User
from questions.models import Question, Option, MasterQuestion, MasterOption, Subject, Topic, SubTopic, Course, SubCourse
from contributor.models import ContributorProfile
from questions.serializers import QuestionSerializer, OptionSerializer, MasterQuestionSerializer, MasterOptionSerializer

def create_test_image(name='test_image.jpg', format='JPEG'):
    """Create a test image file."""
    image = Image.new('RGB', (100, 100), color='red')
    image_file = io.BytesIO()
    image.save(image_file, format=format)
    image_file.seek(0)
    return SimpleUploadedFile(
        name=name,
        content=image_file.getvalue(),
        content_type=f'image/{format.lower()}'
    )

def test_serializer_fields():
    """Test that all serializers include the attachment fields."""
    
    print("🔍 Testing Serializer Fields...")
    
    # Test QuestionSerializer
    question_fields = QuestionSerializer.Meta.fields
    print(f"✅ QuestionSerializer fields: {len(question_fields)} total")
    
    required_question_fields = ['attachments', 'explanation_attachment', 'reason_document', 'explanation', 'reason']
    for field in required_question_fields:
        if field in question_fields:
            print(f"  ✅ {field} - PRESENT")
        else:
            print(f"  ❌ {field} - MISSING")
    
    # Test OptionSerializer
    option_fields = OptionSerializer.Meta.fields
    print(f"\n✅ OptionSerializer fields: {len(option_fields)} total")
    
    if 'attachments' in option_fields:
        print(f"  ✅ attachments - PRESENT")
    else:
        print(f"  ❌ attachments - MISSING")
    
    # Test MasterQuestionSerializer
    master_question_fields = MasterQuestionSerializer.Meta.fields
    print(f"\n✅ MasterQuestionSerializer fields: {len(master_question_fields)} total")
    
    required_master_question_fields = ['attachments', 'reason_document']
    for field in required_master_question_fields:
        if field in master_question_fields:
            print(f"  ✅ {field} - PRESENT")
        else:
            print(f"  ❌ {field} - MISSING")
    
    # Test MasterOptionSerializer
    master_option_fields = MasterOptionSerializer.Meta.fields
    print(f"\n✅ MasterOptionSerializer fields: {len(master_option_fields)} total")
    
    required_master_option_fields = ['attachments', 'reason_document']
    for field in required_master_option_fields:
        if field in master_option_fields:
            print(f"  ✅ {field} - PRESENT")
        else:
            print(f"  ❌ {field} - MISSING")

def test_model_fields():
    """Test that all models have the attachment fields."""
    
    print("\n🔍 Testing Model Fields...")
    
    # Test Question model
    question_fields = [field.name for field in Question._meta.fields]
    print(f"✅ Question model fields: {len(question_fields)} total")
    
    required_question_fields = ['attachments', 'explanation_attachment', 'reason_document', 'explanation', 'reason']
    for field in required_question_fields:
        if field in question_fields:
            print(f"  ✅ {field} - PRESENT")
        else:
            print(f"  ❌ {field} - MISSING")
    
    # Test Option model
    option_fields = [field.name for field in Option._meta.fields]
    print(f"\n✅ Option model fields: {len(option_fields)} total")
    
    if 'attachments' in option_fields:
        print(f"  ✅ attachments - PRESENT")
    else:
        print(f"  ❌ attachments - MISSING")
    
    # Test MasterQuestion model
    master_question_fields = [field.name for field in MasterQuestion._meta.fields]
    print(f"\n✅ MasterQuestion model fields: {len(master_question_fields)} total")
    
    required_master_question_fields = ['attachments', 'reason_document']
    for field in required_master_question_fields:
        if field in master_question_fields:
            print(f"  ✅ {field} - PRESENT")
        else:
            print(f"  ❌ {field} - MISSING")
    
    # Test MasterOption model
    master_option_fields = [field.name for field in MasterOption._meta.fields]
    print(f"\n✅ MasterOption model fields: {len(master_option_fields)} total")
    
    required_master_option_fields = ['attachments', 'reason_document']
    for field in required_master_option_fields:
        if field in master_option_fields:
            print(f"  ✅ {field} - PRESENT")
        else:
            print(f"  ❌ {field} - MISSING")

def test_basic_functionality():
    """Test basic creation with image attachments."""
    
    print("\n🔍 Testing Basic Functionality...")
    
    try:
        # Create test user and contributor
        user, created = User.objects.get_or_create(
            username='testcontributor',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Contributor'
            }
        )
        
        contributor, created = ContributorProfile.objects.get_or_create(
            user=user,
            defaults={'role': 'contributor'}
        )
        
        # Create test subject, topic, course
        subject, created = Subject.objects.get_or_create(name='Test Subject')
        topic, created = Topic.objects.get_or_create(subject=subject, name='Test Topic')
        subtopic, created = SubTopic.objects.get_or_create(topic=topic, name='Test SubTopic')
        course, created = Course.objects.get_or_create(name='Test Course')
        subcourse, created = SubCourse.objects.get_or_create(course=course, name='Test SubCourse')
        
        print("✅ Test data created successfully")
        
        # Test Question creation with attachments
        question_image = create_test_image('question.jpg')
        explanation_image = create_test_image('explanation.jpg')
        reason_image = create_test_image('reason.jpg')
        
        question = Question.objects.create(
            content='Test question with image attachments',
            difficulty=1,
            author=contributor,
            explanation='Test explanation with image',
            reason='Test reason with image',
            attachments=question_image,
            explanation_attachment=explanation_image,
            reason_document=reason_image,
        )
        question.subject.add(subject)
        question.topic.add(topic)
        question.course.add(course)
        
        print(f"✅ Question created with ID: {question.question_id}")
        print(f"  📎 Question attachment: {question.attachments.name if question.attachments else 'None'}")
        print(f"  📎 Explanation attachment: {question.explanation_attachment.name if question.explanation_attachment else 'None'}")
        print(f"  📎 Reason document: {question.reason_document.name if question.reason_document else 'None'}")
        
        # Test Option creation with attachment
        option_image = create_test_image('option.jpg')
        option = Option.objects.create(
            question=question,
            option_text='Test option with image',
            is_correct=True,
            attachments=option_image,
        )
        
        print(f"✅ Option created with ID: {option.option_id}")
        print(f"  📎 Option attachment: {option.attachments.name if option.attachments else 'None'}")
        
        # Test MasterQuestion creation with attachments
        master_image = create_test_image('master_question.jpg')
        master_reason_image = create_test_image('master_reason.jpg')
        
        master_question = MasterQuestion.objects.create(
            title='Test Master Question',
            passage_content='This is a test passage for master question',
            author=contributor,
            attachments=master_image,
            reason='Test reason for master question',
            reason_document=master_reason_image,
        )
        
        print(f"✅ Master Question created with ID: {master_question.master_question_id}")
        print(f"  📎 Master question attachment: {master_question.attachments.name if master_question.attachments else 'None'}")
        print(f"  📎 Master question reason document: {master_question.reason_document.name if master_question.reason_document else 'None'}")
        
        # Test MasterOption creation with attachments
        master_option_image = create_test_image('master_option.jpg')
        master_option_reason_image = create_test_image('master_option_reason.jpg')
        
        master_option = MasterOption.objects.create(
            title='Test Master Option',
            option_content='This is a test option content for master option',
            conditions='Test conditions for master option',
            author=contributor,
            attachments=master_option_image,
            reason='Test reason for master option',
            reason_document=master_option_reason_image,
        )
        
        print(f"✅ Master Option created with ID: {master_option.master_option_id}")
        print(f"  📎 Master option attachment: {master_option.attachments.name if master_option.attachments else 'None'}")
        print(f"  📎 Master option reason document: {master_option.reason_document.name if master_option.reason_document else 'None'}")
        
        print("\n🎉 All image attachment functionality is working correctly!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 Starting Image Attachment Tests...")
    print("=" * 50)
    
    test_model_fields()
    test_serializer_fields()
    test_basic_functionality()
    
    print("\n" + "=" * 50)
    print("✅ Image Attachment Tests Completed!")
