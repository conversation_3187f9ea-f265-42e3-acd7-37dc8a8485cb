from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from firebase_admin import messaging
from firebase_admin.exceptions import InvalidArgumentError

from firebase_admin.exceptions import FirebaseError

from contributor.permissions import IsContributorUser
from customrcare.permissions import IsCustomrcareUser
from django.conf import settings
import json
from rest_framework import serializers, viewsets, status

from .models import FCMDevice, Notification, NotificationImage
from .serializers import FCMDeviceSerializer, NotificationSerializer
from rest_framework.decorators import api_view
from django.db import models
from django.contrib.auth import get_user_model
from django.db.models import Q
class FCMDeviceListCreateAPIView(APIView):
    """
    API for listing and registering FCM devices
    """

    # permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        List all active FCM devices for the logged-in user
        """
        devices = FCMDevice.objects.filter(is_active=True)
        serializer = FCMDeviceSerializer(devices, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

        
    def post(self, request):
        """
        Register a new FCM device or update an existing one
        """
        registration_token = request.data.get("registration_token")

        User = get_user_model()
        user_id = request.data.get("user", None)
        if user_id is not None:
            user_instance = User.objects.filter(id=user_id).first()
            query = Q(user=user_instance)  | (Q(registration_token=registration_token))
        else:
            query = Q(registration_token=registration_token)
        
        existing_device = FCMDevice.objects.filter(query).first()
        print("###########",existing_device)    
        if existing_device:
            for field, value in request.data.items():
                model_field = FCMDevice._meta.get_field(field)

                # Convert JSON fields from string to actual list
                if field in ["interest", "registered_courses"] and isinstance(value, str):
                    try:
                        value = json.loads(value)  # Convert string to Python list
                    except json.JSONDecodeError:
                        pass  # If decoding fails, keep it as-is

                # Convert boolean fields properly
                if isinstance(model_field, models.BooleanField):
                    value = value in ["true", "True", True, 1]

                if field == "user":
                    value = user_instance  # Assign the fetched User instance

                setattr(existing_device, field, value)

            existing_device.save()
            return Response(
                {"message": "Device updated successfully"}, status=status.HTTP_200_OK
            )

        # Create new device
        print("###########",request.data)
        serializer = FCMDeviceSerializer(data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)


class FCMDeviceDetailAPIView(APIView):
    """
    API for retrieving, updating, and deleting an FCM device
    """

    permission_classes = [IsAuthenticated]

    def get_object(self, pk, user):
        try:
            return FCMDevice.objects.get(pk=pk, user=user, is_active=True)
        except FCMDevice.DoesNotExist:
            return None

    def get(self, request, pk):
        """
        Retrieve a specific FCM device
        """
        device = self.get_object(pk, request.user)
        if not device:
            return Response(
                {"error": "Device not found"}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = FCMDeviceSerializer(device)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk):
        """
        Update a specific FCM device
        """
        device = self.get_object(pk, request.user)
        if not device:
            return Response(
                {"error": "Device not found"}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = FCMDeviceSerializer(device, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request, pk):
        """
        Delete a specific FCM device
        """
        device = self.get_object(pk, request.user)
        if not device:
            return Response(
                {"error": "Device not found"}, status=status.HTTP_404_NOT_FOUND
            )

        device.is_active = False
        device.save()
        return Response(
            {"message": "Device deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )


# @api_view(["POST"])
# def send_test_notification(request):
#     registration_token = request.data.get("registration_token")

#     if not registration_token:
#         return Response(
#             {"error": "Registration token is required"},
#             status=status.HTTP_400_BAD_REQUEST,
#         )

#     # Check if the token exists in the database and is active
#     fcm_device = FCMDevice.objects.filter(
#         registration_token=registration_token, is_active=True
#     ).first()
#     if not fcm_device:
#         return Response(
#             {"error": "Invalid or inactive registration token"},
#             status=status.HTTP_400_BAD_REQUEST,
#         )
#     print(fcm_device)
#     try:
#         # Prepare the notification message
#         message = messaging.Message(
#             notification=messaging.Notification(
#                 title="Test Notification",
#                 body="This is a test notification sent via FCM.",
#             ),
#             token=registration_token,
#         )
#         print(message)

#         # Send notification
#         response = messaging.send(message)
#         print(f"Successfully sent message: {response}")

#         return Response(
#             {"message": "Notification sent successfully", "response": response},
#             status=status.HTTP_200_OK,
#         )

#     except FirebaseError as e:  # Correct exception handling
#         print(f"Error sending FCM notification: {e}")
#         return Response(
#             {"error": f"Failed to send notification: {str(e)}"},
#             status=status.HTTP_500_INTERNAL_SERVER_ERROR,
#         )


# @api_view(["POST"])
# def send_test_notification(request):
#     registration_tokens = request.data.get("registration_tokens")
#     title = request.data.get("title", "Default Title")
#     body = request.data.get("body", "Default Body")
#     description = request.data.get("description", "")
#     image = request.data.get("image", None)
#     extra_data = request.data.get("extra_data", {})

#     if not registration_tokens:
#         fcm_devices = FCMDevice.objects.filter(is_active=True)
#         if not fcm_devices.exists():
#             return Response(
#                 {"error": "No active devices found"},
#                 status=status.HTTP_404_NOT_FOUND,
#             )
#         registration_tokens = [device.registration_token for device in fcm_devices]

#     if not registration_tokens:
#         return Response(
#             {"error": "No registration tokens provided"},
#             status=status.HTTP_400_BAD_REQUEST,
#         )

#     try:
#         notification = messaging.Notification(
#             title=title,
#             body=body,
#             image=image,
#         )

#         responses = []
#         for token in registration_tokens:
#             message = messaging.Message(
#                 notification=notification,
#                 token=token,
#                 data=extra_data,
#             )
#             try:
#                 response = messaging.send(message)
#                 responses.append(
#                     {"token": token, "status": "success", "response": response}
#                 )
#             except FirebaseError as e:
#                 responses.append({"token": token, "status": "error", "error": str(e)})

#         return Response(
#             {
#                 "message": "Notifications processed",
#                 "responses": responses,
#                 "description": description,
#             },
#             status=status.HTTP_200_OK,
#         )

#     except Exception as e:
#         return Response(
#             {"error": f"Failed to process notifications: {str(e)}"},
#             status=status.HTTP_500_INTERNAL_SERVER_ERROR,
#         )


from rest_framework.views import APIView
from django.conf import settings
from firebase_admin.exceptions import InvalidArgumentError
@api_view(["POST"])
def send_test_notification(request):
    data = request.data

    # Extract registration tokens correctly
    registration_tokens = data.get("registration_tokens") or data.get("registration_ids", [])
    if isinstance(registration_tokens, str):
        registration_tokens = [registration_tokens] 

    notification_data = data.get("notification", {})
    extra_data = data.get("data", {})

    # Extract notification fields properly
    title = notification_data.get("title", "Default Title")
    body = notification_data.get("body", "Default Body")
    image = notification_data.get("image", None)
    description = notification_data.get("description", "")

    # d_title = extra_data.get("title", "Default Title")
    # d_body = extra_data.get("body", "Default Body")
    # d_image = extra_data.get("image", None)
    # d_description = extra_data.get("description", "")

    # Handle empty registration tokens
    if not registration_tokens:
        fcm_devices = FCMDevice.objects.filter(is_active=True)
        if not fcm_devices.exists():
            return Response(
                {"error": "No active devices found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        registration_tokens = [device.registration_token for device in fcm_devices]
    
    if not registration_tokens:
        return Response(
            {"error": "No registration tokens provided"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        # Create notification object if title or body is present
        notification = None
        if title or body or image:
            notification = messaging.Notification(
                title=title,
                body=body,
                image=image,
            )

        responses = []
        for token in registration_tokens:
            # Create message with notification and data
            message = messaging.Message(
                notification=notification,
                token=token,
                data=extra_data,  # Properly send data for onMessage() handling
            )

            try:
                response = messaging.send(message)
                # Notification.objects.create(
                #     n_title=title,
                #     n_body=body,
                #     n_image=image,
                #     n_description=description,
                #     d_title=d_title,
                #     d_body=d_body,
                #     d_image=image,
                #     d_description=d_description
                # )   

                responses.append(
                    {"token": token, "status": "success", "response": response}
                )
            except InvalidArgumentError as e:
                # Handle invalid tokens properly
                if "not found" in str(e):
                    print(f"Invalid token found: {token}")
                    responses.append({"token": token, "status": "error", "error": "Token not found"})
                else:
                    responses.append({"token": token, "status": "error", "error": str(e)})
            except Exception as e:
                responses.append({"token": token, "status": "error", "error": str(e)})

        return Response(
            {
                "message": "Notifications processed successfully",
                "responses": responses,
                # "description": description,
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        return Response(
            {"error": f"Failed to process notifications: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
    


class FCMDeviceView(APIView):
    """
    API to update interest and registered_courses using registration_token
    """

    def get(self, request):
        """
        Get FCMDevice details using registration_token
        """
        registration_token = request.query_params.get("registration_token")
        if not registration_token:
            return Response({"error": "registration_token is required"}, status=400)

        device = FCMDevice.objects.filter(registration_token=registration_token).first()
        if not device:
            return Response({"error": "Device not found"}, status=404)

        serializer = FCMDeviceSerializer(device)
        return Response(serializer.data, status=200)

    def patch(self, request):
        """
        Update interest and registered_courses using registration_token
        """
        registration_token = request.data.get("registration_token")
        if not registration_token:
            return Response({"error": "registration_token is required"}, status=400)

        device = FCMDevice.objects.filter(registration_token=registration_token).first()
        if not device:
            return Response({"error": "Device not found"}, status=404)

        interest = request.data.get("interest")
        registered_courses = request.data.get("registered_courses")

        if interest is not None:
            if not isinstance(interest, list):
                return Response({"error": "interest must be a list"}, status=400)
            device.interest = interest

        if registered_courses is not None:
            if not isinstance(registered_courses, list):
                return Response({"error": "registered_courses must be a list"}, status=400)
            device.registered_courses = registered_courses

        device.save()

        return Response(
            {"message": "Device updated successfully", "updated_data": {
                "interest": device.interest,
                "registered_courses": device.registered_courses
            }},
            status=200
        )
from rest_framework.parsers import MultiPartParser, FormParser
    
# Menu Notification ViewSet
class NotificationViewSet(viewsets.ModelViewSet):
    """
    API for CRUD operations on notifications
    """
    permission_classes = [IsCustomrcareUser]
    queryset = Notification.objects.all().order_by('-created_at')
    serializer_class = NotificationSerializer
    parser_classes = [MultiPartParser, FormParser]  # Allow file uploads
    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()

        try:
            notification_data = json.loads(request.data.get('notification', '{}'))
            data_data = json.loads(request.data.get('data', '{}'))
        except json.JSONDecodeError:
            return Response({"error": "Invalid JSON format"}, status=status.HTTP_400_BAD_REQUEST)

         # ✅ Only update fields that are present in the request
        instance.n_title = notification_data.get('title', instance.n_title)
        instance.n_body = notification_data.get('body', instance.n_body)
        instance.n_description = notification_data.get('description', instance.n_description)
        instance.icon = notification_data.get('icon', instance.icon)
        instance.d_url = data_data.get('url', instance.d_url)
        instance.d_title = data_data.get('title', instance.d_title)
        instance.d_body = data_data.get('body', instance.d_body)
        instance.d_description = data_data.get('description', instance.d_description)

        # ✅ Check if the file exists in FILES and is a real file
        if 'n_image' in request.FILES and request.FILES['n_image']:
            instance.n_image = request.FILES['n_image']

        if 'd_image' in request.FILES and request.FILES['d_image']:
            instance.d_image = request.FILES['d_image']

        instance.save()
        return Response(NotificationSerializer(instance, context={"request": request}).data)
    
@api_view(["POST"])
def notification_image(request):
    if request.method == 'POST':
        image = request.FILES.get('image')
        if image:
            notification_image = NotificationImage.objects.create(image=image)
            # Generate full absolute URL for the uploaded image
            full_image_url = request.build_absolute_uri(notification_image.image.url)
        
            return Response({"message": "Image uploaded successfully", "image_url": full_image_url}, status=201)
        else:
            return Response({"error": "No image provided"}, status=400)
    return Response({"error": "Invalid request method"}, status=405)    