from rest_framework import serializers
from paper_engine.models import TestPattern
from paper_engine.serializers import TestPatternSerializer

from .models import (
    Question,
    Option,
    Course,
    SubCourse,
    Tier,
    Paper,
    Section,
    Module,
    MasterQuestion,
    MasterOption,
    SubTopic,
    Subject,
    Topic,
    PreviousYearQuestion,
)


class ModuleSerializer(serializers.ModelSerializer):
    test_pattern_details = TestPatternSerializer(source="test_pattern", read_only=True)
    test_pattern = serializers.PrimaryKeyRelatedField(
        queryset=TestPattern.objects.all(), write_only=True
    )
    section_name = serializers.CharField(source="section.name", read_only=True)

    class Meta:
        model = Module
        fields = [
            "module_id",
            "slug",
            "section",
            "section_name",
            "name",
            "description",
            "test_pattern",
            "test_pattern_details",
        ]

    # def update(self, instance, validated_data):
    #     test_pattern_data = validated_data.pop("test_pattern", None)
    #     instance.test_pattern = test_pattern_data
    #     for attr, value in validated_data.items():
    #         setattr(instance, attr, value)
    #     instance.save()
    #     return instance


class SectionSerializer(serializers.ModelSerializer):
    modules = ModuleSerializer(many=True, read_only=True)
    paper_name = serializers.CharField(source="paper.name", read_only=True)
    test_pattern_details = serializers.SerializerMethodField()
    test_pattern = serializers.PrimaryKeyRelatedField(
        queryset=TestPattern.objects.all(),
        write_only=True,
        required=False,
        allow_null=True,
    )

    class Meta:
        model = Section
        fields = [
            "section_id",
            "slug",
            "paper",
            "paper_name",
            "name",
            "description",
            "max_marks",
            "number_of_questions",
            "test_pattern",
            "test_pattern_details",
            "modules",
        ]

    def get_test_pattern_details(self, obj):
        if obj.test_pattern:
            return TestPatternSerializer(obj.test_pattern).data
        return None

    def update(self, instance, validated_data):
        test_pattern_data = validated_data.pop("test_pattern", None)
        instance.test_pattern = test_pattern_data
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class PaperSerializer(serializers.ModelSerializer):
    sections = SectionSerializer(many=True, read_only=True)
    tier_name = serializers.CharField(source="tier.name", read_only=True)
    test_pattern_details = serializers.SerializerMethodField()
    test_pattern = serializers.PrimaryKeyRelatedField(
        queryset=TestPattern.objects.all(),
        write_only=True,
        required=False,
        allow_null=True,
    )

    class Meta:
        model = Paper
        fields = [
            "paper_id",
            "slug",
            "tier",
            "tier_name",
            "name",
            "description",
            "max_marks",
            "duration",
            "test_pattern",
            "test_pattern_details",
            "sections",
        ]

    def get_test_pattern_details(self, obj):
        if obj.test_pattern:
            return TestPatternSerializer(obj.test_pattern).data
        return None

    def update(self, instance, validated_data):
        test_pattern_data = validated_data.pop("test_pattern", None)
        instance.test_pattern = test_pattern_data
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class TierSerializer(serializers.ModelSerializer):
    papers = PaperSerializer(many=True, read_only=True)
    subcourse_name = serializers.CharField(source="subcourse.name", read_only=True)
    test_pattern_details = serializers.SerializerMethodField()
    test_pattern = serializers.PrimaryKeyRelatedField(
        queryset=TestPattern.objects.all(),
        write_only=True,
        required=False,
        allow_null=True,
    )

    class Meta:
        model = Tier
        fields = [
            "tier_id",
            "slug",
            "subcourse",
            "subcourse_name",
            "name",
            "description",
            "test_pattern",
            "test_pattern_details",
            "papers",
        ]

    def get_test_pattern_details(self, obj):
        if obj.test_pattern:
            return TestPatternSerializer(obj.test_pattern).data
        return None

    def update(self, instance, validated_data):
        test_pattern_data = validated_data.pop("test_pattern", None)
        instance.test_pattern = test_pattern_data
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class SubCourseSerializer(serializers.ModelSerializer):
    tiers = TierSerializer(many=True, read_only=True)
    course_name = serializers.CharField(source="course.name", read_only=True)

    class Meta:
        model = SubCourse
        fields = [
            "subcourse_id",
            "slug",
            "course",
            "course_name",
            "name",
            "description",
            "tiers",
        ]


class CourseSerializer(serializers.ModelSerializer):
    sub_courses = serializers.SerializerMethodField()

    class Meta:
        model = Course
        fields = [
            "course_id",
            "slug",
            "name",
            "description",
            "attachments",
            "sub_courses",
        ]

    def get_sub_courses(self, obj):
        sub_courses = obj.sub_courses.all().order_by("-created_date")  # or any other field
        return SubCourseSerializer(sub_courses, many=True).data


class SubTopicSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubTopic
        fields = ["subtopic_id", "slug", "topic", "name", "description"]


class TopicSerializer(serializers.ModelSerializer):
    subtopics = SubTopicSerializer(many=True, read_only=True)

    class Meta:
        model = Topic
        fields = ["topic_id", "slug", "subject", "name", "description", "subtopics"]


class SubjectSerializer(serializers.ModelSerializer):
    topics = TopicSerializer(many=True, read_only=True)

    class Meta:
        model = Subject
        fields = ["subject_id", "slug", "name", "description", "topics", "rank"]


class OptionSerializer(serializers.ModelSerializer):
    question = serializers.PrimaryKeyRelatedField(queryset=Question.objects.all())

    class Meta:
        model = Option
        fields = [
            "option_id",
            "question",
            "option_text",
            'slug',
            "is_correct",
            "created_at",
            "attachments",
        ]

    def create(self, validated_data):
        return Option.objects.create(**validated_data)

    def update(self, instance, validated_data):
        instance.option_text = validated_data.get("option_text", instance.option_text)
        instance.is_correct = validated_data.get("is_correct", instance.is_correct)
        if "attachments" in validated_data:
            instance.attachments = validated_data.get("attachments", instance.attachments)
        instance.save()
        return instance


# class QuestionSerializer(serializers.ModelSerializer):
#     course = serializers.SlugRelatedField(
#         queryset=Course.objects.all(),
#         slug_field="slug",  # Ensure 'slug' is the correct field name in your Course model
#         required=False,
#         many=True,
#     )
#     subcourse = serializers.SlugRelatedField(
#         queryset=SubCourse.objects.all(),
#         slug_field="slug",  # Ensure 'slug' is the correct field name in your SubCourse model
#         required=False,
#         many=True,
#     )
#     subject = serializers.SlugRelatedField(
#         queryset=Subject.objects.all(),
#         slug_field="slug",  # Ensure 'slug' is the correct field name in your Subject model
#         required=False,
#         many=True,
#     )
#     subject_name = serializers.ListField(child=serializers.CharField(), write_only=True)
#     topic = serializers.SlugRelatedField(
#         queryset=Topic.objects.all(),
#         slug_field="slug",  # Ensure 'slug' is the correct field name in your Topic model
#         required=False,
#         many=True,
#     )
#     topic_name = serializers.ListField(child=serializers.CharField(), write_only=True)
#     sub_topic = serializers.SlugRelatedField(
#         queryset=SubTopic.objects.all(),
#         slug_field="slug",  # Ensure 'slug' is the correct field name in your SubTopic model
#         required=False,
#         many=True,
#     )
#     sub_topic_name = serializers.ListField(
#         child=serializers.CharField(), write_only=True
#     )
#     master_question = serializers.PrimaryKeyRelatedField(
#         queryset=MasterQuestion.objects.all(), required=False, allow_null=True
#     )
#     is_master = serializers.BooleanField(default=False)
#     master_option = serializers.PrimaryKeyRelatedField(
#         queryset=MasterOption.objects.all(), required=False, allow_null=True
#     )
#     is_master_option = serializers.BooleanField(default=False)

#     options = OptionSerializer(many=True, read_only=True)
#     # current_affairs = BlogPostSerializer(many=True, read_only=True, required=False)
#     previous_year_questions = serializers.SerializerMethodField()

#     class Meta:
#         model = Question
#         fields = [
#             "question_id",
#             "slug",
#             "subject",
#             "subject_name",
#             "topic",
#             "topic_name",
#             "sub_topic",
#             "sub_topic_name",
#             "content",
#             "difficulty",
#             "author",
#             "current_affairs",
#             "is_current_affairs",
#             "created_at",
#             "status",
#             "approval_status",
#             "updated_by_custmorcare",
#             "updated_at",
#             "average_score",
#             "times_attempted",
#             "attachments",
#             "language",
#             "course",
#             "subcourse",
#             "master_question",
#             "is_master",
#             "master_option",
#             "is_master_option",
#             "previous_year_questions",
#             "options",
#         ]

#     def get_previous_year_questions(self, obj):
#         # Retrieve all PreviousYearQuestions related to this question
#         previous_year_questions = PreviousYearQuestion.objects.filter(question=obj)
#         # Customize how much data is serialized to avoid recursion
#         return [
#             {
#                 "id": pyq.id,
#                 "year": pyq.year,
#                 "month": pyq.month,
#                 "course": {
#                     "id": pyq.course.course_id,
#                     "name": pyq.course.name,
#                 },
#                 "exams": {
#                     "id": pyq.exams.subcourse_id,
#                     "name": pyq.exams.name,
#                 },
#                 "status": pyq.status,
#                 "note": pyq.note,
#                 "created_at": pyq.created_at,
#             }
#             for pyq in previous_year_questions
#         ]

#     def create(self, validated_data):
#         # Handle subcourse as a list of primary keys
#         subject_data = validated_data.pop("subject", [])
#         validated_data["subject_name"] = ",".join(
#             validated_data.pop("subject_name", [])
#         )
#         topic_data = validated_data.pop("topic", [])
#         validated_data["topic_name"] = ",".join(validated_data.pop("topic_name", []))
#         sub_topic_data = validated_data.pop("sub_topic", [])
#         validated_data["sub_topic_name"] = ",".join(
#             validated_data.pop("sub_topic_name", [])
#         )
#         course_data = validated_data.pop("course", [])
#         subcourse_data = validated_data.pop("subcourse", [])
#         question = Question.objects.create(**validated_data)

#         if subject_data:
#             question.subject.set(subject_data)

#         if topic_data:
#             question.topic.set(topic_data)

#         if sub_topic_data:
#             question.sub_topic.set(sub_topic_data)

#         if course_data:
#             question.course.set(course_data)

#         if subcourse_data:
#             question.subcourse.set(subcourse_data)

#         return question


#     def update(self, instance, validated_data):

#         subject_data = validated_data.pop("subject", [])
#         topic_data = validated_data.pop("topic", [])
#         sub_topic_data = validated_data.pop("sub_topic", [])

#         course_data = validated_data.pop("course", [])
#         subcourse_data = validated_data.pop("subcourse", [])

#         instance.content = validated_data.get("content", instance.content)
#         instance.difficulty = validated_data.get("difficulty", instance.difficulty)
#         instance.author = validated_data.get("author", instance.author)
#         instance.status = validated_data.get("status", instance.status)
#         instance.current_affairs = validated_data.get(
#             "current_affairs", instance.current_affairs
#         )
#         instance.approval_status = validated_data.get(
#             "approval_status", instance.approval_status
#         )
#         instance.average_score = validated_data.get(
#             "average_score", instance.average_score
#         )
#         instance.times_attempted = validated_data.get(
#             "times_attempted", instance.times_attempted
#         )
#         instance.subject = validated_data.get("subject", instance.subject)
#         instance.topic = validated_data.get("topic", instance.topic)
#         instance.sub_topic = validated_data.get("sub_topic", instance.sub_topic)
#         instance.master_question = validated_data.get(
#             "master_question", instance.master_question
#         )
#         instance.is_master = validated_data.get("is_master", instance.is_master)

#         instance.save()

#         if subject_data:
#             instance.subject.set(subject_data)

#         if topic_data:
#             instance.topic.set(topic_data)

#         if sub_topic_data:
#             instance.sub_topic.set(sub_topic_data)

#         if course_data:
#             instance.course.set(course_data)
#         if subcourse_data:
#             instance.subcourse.set(subcourse_data)

#         return instance


from rest_framework import serializers
from .models import (
    Question,
    Course,
    SubCourse,
    Subject,
    Topic,
    SubTopic,
    MasterQuestion,
    MasterOption,
    PreviousYearQuestion,
)


class QuestionSerializer(serializers.ModelSerializer):
    course = serializers.SlugRelatedField(
        queryset=Course.objects.all(),
        slug_field="slug",
        required=False,
        many=True,
    )
    subcourse = serializers.SlugRelatedField(
        queryset=SubCourse.objects.all(),
        slug_field="slug",
        required=False,
        many=True,
    )
    subject = serializers.SlugRelatedField(
        queryset=Subject.objects.all(),
        slug_field="slug",
        required=False,
        many=True,
    )
    subject_name = serializers.SerializerMethodField()
    topic = serializers.SlugRelatedField(
        queryset=Topic.objects.all(),
        slug_field="slug",
        required=False,
        many=True,
    )
    topic_name = serializers.SerializerMethodField()
    sub_topic = serializers.SlugRelatedField(
        queryset=SubTopic.objects.all(),
        slug_field="slug",
        required=False,
        many=True,
    )
    sub_topic_name = serializers.SerializerMethodField()
    master_question = serializers.PrimaryKeyRelatedField(
        queryset=MasterQuestion.objects.all(), required=False, allow_null=True
    )
    is_master = serializers.BooleanField(default=False)
    master_option = serializers.PrimaryKeyRelatedField(
        queryset=MasterOption.objects.all(), required=False, allow_null=True
    )
    is_master_option = serializers.BooleanField(default=False)

    options = OptionSerializer(many=True, read_only=True)
    previous_year_questions = serializers.SerializerMethodField()

    class Meta:
        model = Question
        fields = [
            "question_id",
            "slug",
            "subject",
            "subject_name",
            "topic",
            "topic_name",
            "sub_topic",
            "sub_topic_name",
            "content",
            "difficulty",
            "author",
            "current_affairs",
            "is_current_affairs",
            "created_at",
            "status",
            "approval_status",
            "updated_by_custmorcare",
            "updated_at",
            "average_score",
            "times_attempted",
            "attachments",
            "language",
            "course",
            "subcourse",
            "master_question",
            "is_master",
            "master_option",
            "is_master_option",
            "previous_year_questions",
            "options",
            "reason",
            "reason_document",
            "explanation",
            "explanation_attachment",
        ]
    def get_subject_name(self, obj):
        return [subject.name for subject in obj.subject.all()]
    
    def get_topic_name(self, obj):
        return [topic.name for topic in obj.topic.all()]
    def get_sub_topic_name(self, obj):
        return [sub_topic.name for sub_topic in obj.sub_topic.all()]
    
    def get_previous_year_questions(self, obj):
        previous_year_questions = PreviousYearQuestion.objects.filter(question=obj)
        return [
            {
                "id": pyq.id,
                "year": pyq.year,
                "month": pyq.month,
                "course": {
                    "id": pyq.course.course_id,
                    "name": pyq.course.name,
                },
                "exams": {
                    "id": pyq.exams.subcourse_id,
                    "name": pyq.exams.name,
                },
                "status": pyq.status,
                "note": pyq.note,
                "created_at": pyq.created_at,
            }
            for pyq in previous_year_questions
        ]

    def create(self, validated_data):
        print("validated_data", validated_data)
        subject_data = validated_data.pop("subject", [])
        validated_data["subject_name"] = validated_data.pop("subject_name", [])
        topic_data = validated_data.pop("topic", [])
        validated_data["topic_name"] = validated_data.pop("topic_name", [])
        sub_topic_data = validated_data.pop("sub_topic", [])
        validated_data["sub_topic_name"] = validated_data.pop("sub_topic_name", [])
        course_data = validated_data.pop("course", [])
        subcourse_data = validated_data.pop("subcourse", [])
        

        question = Question.objects.create(**validated_data)
        if subject_data:
            question.subject.set(subject_data)

        if topic_data:
            question.topic.set(topic_data)

        if sub_topic_data:
            question.sub_topic.set(sub_topic_data)

        if course_data:
            question.course.set(course_data)

        if subcourse_data:
            question.subcourse.set(subcourse_data)
        

        return question

    def update(self, instance, validated_data):
        subject_data = validated_data.pop("subject", [])
        topic_data = validated_data.pop("topic", [])
        sub_topic_data = validated_data.pop("sub_topic", [])
        course_data = validated_data.pop("course", [])
        subcourse_data = validated_data.pop("subcourse", [])

        instance.content = validated_data.get("content", instance.content)
        instance.difficulty = validated_data.get("difficulty", instance.difficulty)
        instance.author = validated_data.get("author", instance.author)
        instance.status = validated_data.get("status", instance.status)
        instance.current_affairs = validated_data.get(
            "current_affairs", instance.current_affairs
        )
        instance.approval_status = validated_data.get(
            "approval_status", instance.approval_status
        )
        instance.average_score = validated_data.get(
            "average_score", instance.average_score
        )
        instance.times_attempted = validated_data.get(
            "times_attempted", instance.times_attempted
        )
        instance.master_question = validated_data.get(
            "master_question", instance.master_question
        )
        instance.is_master = validated_data.get("is_master", instance.is_master)
        instance.explanation = validated_data.get("explanation", instance.explanation)
        instance.reason = validated_data.get("reason", instance.reason)
        if "attachments" in validated_data:
            instance.attachments = validated_data.get("attachments", instance.attachments)
        if "explanation_attachment" in validated_data:
            instance.explanation_attachment = validated_data.get("explanation_attachment", instance.explanation_attachment)
        if "reason_document" in validated_data:
            instance.reason_document = validated_data.get("reason_document", instance.reason_document)

        instance.save()

        if subject_data:
            instance.subject.set(subject_data)

        if topic_data:
            instance.topic.set(topic_data)

        if sub_topic_data:
            instance.sub_topic.set(sub_topic_data)

        if course_data:
            instance.course.set(course_data)

        if subcourse_data:
            instance.subcourse.set(subcourse_data)

        return instance


class MasterQuestionSerializer(serializers.ModelSerializer):
    # Only show the primary keys for related questions
    questions = QuestionSerializer(many=True, read_only=True)
    # current_affairs = BlogPostSerializer(read_only=True)

    class Meta:
        model = MasterQuestion
        fields = [
            "master_question_id",
            "slug",
            "author",
            "title",
            "passage_content",
            "current_affairs",
            "is_current_affairs",
            "approval_status",
            "created_at",
            "updated_by_custmorcare",
            "updated_at",
            "questions",
            "reason",
            "reason_document",
            "attachments"
        ]

    def create(self, validated_data):
        return MasterQuestion.objects.create(**validated_data)

    def update(self, instance, validated_data):
        instance.title = validated_data.get("title", instance.title)
        instance.passage_content = validated_data.get(
            "passage_content", instance.passage_content
        )
        if "attachments" in validated_data:
            instance.attachments = validated_data.get("attachments", instance.attachments)
        if "reason_document" in validated_data:
            instance.reason_document = validated_data.get("reason_document", instance.reason_document)
        instance.save()
        return instance


class MasterOptionSerializer(serializers.ModelSerializer):
    related_questions = QuestionSerializer(many=True, read_only=True)
    # current_affairs = BlogPostSerializer(many=True, read_only=True)

    class Meta:
        model = MasterOption
        fields = [
            "master_option_id",
            "slug",
            "author",
            "title",
            "option_content",
            "conditions",
            "current_affairs",
            "is_current_affairs",
            "approval_status",
            "created_at",
            "updated_by_custmorcare",
            "updated_at",
            "related_questions",
            "reason",
            "reason_document",
            "attachments"
        ]

    def create(self, validated_data):
        return MasterOption.objects.create(**validated_data)

    def update(self, instance, validated_data):
        instance.title = validated_data.get("title", instance.title)
        instance.option_content = validated_data.get("option_content", instance.option_content)
        instance.conditions = validated_data.get("conditions", instance.conditions)
        if "attachments" in validated_data:
            instance.attachments = validated_data.get("attachments", instance.attachments)
        if "reason_document" in validated_data:
            instance.reason_document = validated_data.get("reason_document", instance.reason_document)
        instance.save()
        return instance

    def update(self, instance, validated_data):
        instance.title = validated_data.get("title", instance.title)
        instance.option_content = validated_data.get(
            "option_content", instance.option_content
        )
        instance.conditions = validated_data.get("conditions", instance.conditions)
        instance.save()
        return instance


class PreviousYearQuestionSerializer(serializers.ModelSerializer):
    question_details = QuestionSerializer(source="question", read_only=True)
    course = serializers.PrimaryKeyRelatedField(
        queryset=Course.objects.all(),
        required=False,
    )
    exams = serializers.PrimaryKeyRelatedField(
        queryset=SubCourse.objects.all(),
        required=False,
    )

    class Meta:
        model = PreviousYearQuestion
        fields = [
            "id",
            "slug",
            "question",
            "question_details",
            "year",
            "month",
            "course",
            "exams",
            "status",
            "note",
            "created_at",
        ]

    def get_question_details(self, obj):
        # Fetch all necessary question details including options
        return {
            "id": obj.question.question_id,
            "content": obj.question.content,
            "difficulty": obj.question.get_difficulty_level(),
            "author": obj.question.author.user.username,  # Assuming the author has a username field.
            "options": OptionSerializer(obj.question.options.all(), many=True).data,
        }

    # def get_year_list(self, obj):
    #     return obj.get_year_list()

    # def get_month_list(self, obj):
    #     return obj.get_month_list()

    # def get_exam_list(self, obj):
    #     return obj.get_exam_list()

    # def validate_year(self, value):
    #     from datetime import datetime

    #     if value > datetime.now().year:
    #         raise serializers.ValidationError("Year cannot be in the future.")
    #     return value

