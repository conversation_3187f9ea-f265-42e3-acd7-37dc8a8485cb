from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import RegexValidator
from students.models import Student
import re


def normalize_phone_number(phone):
    """
    Enhanced phone number normalization with strict validation.

    Features:
    - Filters out numbers with less than 10 digits
    - Preserves # and * characters for special numbers
    - Removes country codes and formatting
    - Validates Indian mobile number patterns

    Returns:
    - Clean 10-digit number for valid Indian mobile numbers
    - Original string for special numbers containing # or *
    - None for invalid numbers (less than 10 digits)
    """
    if not phone:
        return None

    phone_str = str(phone).strip()

    # Check for special characters # and * - preserve these numbers as-is
    if '#' in phone_str or '*' in phone_str:
        # For special numbers, just clean basic formatting but keep # and *
        special_cleaned = re.sub(r'[^\d#*+\-\s()]', '', phone_str)
        special_cleaned = re.sub(r'\s+', '', special_cleaned)  # Remove spaces

        # Validate special number length (must be between 3-20 characters)
        if len(special_cleaned) < 3 or len(special_cleaned) > 20:
            return None

        return special_cleaned

    # For regular numbers, remove all non-digit characters
    cleaned = re.sub(r'\D', '', phone_str)

    # Early filter: If less than 10 digits after cleaning, reject immediately
    if len(cleaned) < 10:
        return None

    # Remove country code if present
    if cleaned.startswith('91') and len(cleaned) == 12:
        cleaned = cleaned[2:]
    elif cleaned.startswith('1') and len(cleaned) == 11:
        cleaned = cleaned[1:]

    # Final validation: Must be exactly 10 digits and start with valid Indian mobile prefix
    if len(cleaned) == 10 and cleaned.startswith(('6', '7', '8', '9')):
        return cleaned

    # If more than 10 digits but doesn't match patterns, reject
    if len(cleaned) > 10:
        return None

    # If exactly 10 digits but invalid prefix, reject
    if len(cleaned) == 10:
        return None

    return None  # Return None for invalid numbers


class Contact(models.Model):
    """
    Streamlined Contact model for temporary storage and matching.

    This single model handles:
    - Temporary storage of synced contacts
    - Matching with registered users
    - Automatic cleanup of unmatched contacts
    """

    # Core fields
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='synced_contacts',
        help_text="The user who synced this contact"
    )

    contact_number = models.CharField(
        max_length=15,
        help_text="Normalized phone number of the contact"
    )

    name = models.CharField(
        max_length=255,
        blank=True,
        help_text="Contact name as stored in user's phone"
    )

    # Matching fields
    related_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='contact_relationships',
        help_text="The registered user this contact matches (if any)"
    )

    is_matched = models.BooleanField(
        default=False,
        help_text="Whether this contact matches a registered user"
    )

    # Timing fields
    synced_at = models.DateTimeField(
        auto_now_add=True,
        help_text="When this contact was synced"
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        help_text="Last update time"
    )

    # Optional metadata
    raw_data = models.JSONField(
        null=True,
        blank=True,
        help_text="Additional contact data from phone"
    )

    class Meta:
        unique_together = ['user', 'contact_number']
        indexes = [
            models.Index(fields=['user', 'contact_number']),
            models.Index(fields=['contact_number']),
            models.Index(fields=['is_matched']),
            models.Index(fields=['synced_at']),
            models.Index(fields=['user', 'is_matched']),
        ]
        verbose_name = "Contact"
        verbose_name_plural = "Contacts"

    def __str__(self):
        status = "Matched" if self.is_matched else "Unmatched"
        related_info = f" -> {self.related_user.username}" if self.related_user else ""
        return f"{self.user.username}: {self.name} ({self.contact_number}) [{status}]{related_info}"

    def save(self, *args, **kwargs):
        # Normalize phone number before saving
        self.contact_number = normalize_phone_number(self.contact_number)

        # Check for matching registered user
        self.check_and_match_user()

        super().save(*args, **kwargs)

    def check_and_match_user(self):
        """
        Check if this contact number matches a registered user.
        If match found, set related_user and is_matched=True.
        """
        try:
            # Try to find a student with this phone number
            student = Student.objects.select_related('user').get(phone=self.contact_number)

            # Don't match with self
            if student.user != self.user:
                self.related_user = student.user
                self.is_matched = True
                return True

        except Student.DoesNotExist:
            pass

        # No match found
        self.related_user = None
        self.is_matched = False
        return False

    @classmethod
    def cleanup_unmatched_contacts(cls, hours=24):
        """
        Delete unmatched contacts older than specified hours.
        Returns the number of deleted contacts.
        """
        cutoff_time = timezone.now() - timezone.timedelta(hours=hours)

        unmatched_contacts = cls.objects.filter(
            is_matched=False,
            synced_at__lt=cutoff_time
        )

        count = unmatched_contacts.count()
        unmatched_contacts.delete()

        return count

    @classmethod
    def get_user_matches(cls, user):
        """
        Get all matched contacts for a user.
        Returns QuerySet of contacts where is_matched=True.
        """
        return cls.objects.filter(
            user=user,
            is_matched=True
        ).select_related('related_user')

    @classmethod
    def sync_contacts_for_user(cls, user, contacts_data):
        """
        Sync contacts for a user with deduplication.

        Args:
            user: User object
            contacts_data: List of dicts with 'name' and 'contact' keys

        Returns:
            dict with sync results
        """
        created_count = 0
        updated_count = 0
        matched_count = 0
        errors = []

        for contact_data in contacts_data:
            try:
                name = contact_data.get('name', '').strip()
                contact_number = contact_data.get('contact', '').strip()

                if not contact_number:
                    errors.append(f"Empty contact number for {name}")
                    continue

                # Normalize the phone number
                normalized_number = normalize_phone_number(contact_number)

                # Enhanced validation with detailed error messages
                if normalized_number is None:
                    errors.append(f"Invalid phone number '{contact_number}': Less than 10 digits or invalid format")
                    continue

                # Check if it's a special number (contains # or *)
                is_special_number = '#' in normalized_number or '*' in normalized_number

                # For regular numbers, ensure exactly 10 digits
                if not is_special_number and len(normalized_number) != 10:
                    errors.append(f"Invalid phone number '{contact_number}': Must be exactly 10 digits")
                    continue

                # For special numbers, allow different lengths but ensure reasonable limits
                if is_special_number and (len(normalized_number) < 3 or len(normalized_number) > 20):
                    errors.append(f"Invalid special number '{contact_number}': Length must be between 3-20 characters")
                    continue

                # Get or create contact (deduplication)
                contact, created = cls.objects.get_or_create(
                    user=user,
                    contact_number=normalized_number,
                    defaults={
                        'name': name,
                        'raw_data': contact_data
                    }
                )

                if created:
                    created_count += 1
                else:
                    # Update existing contact
                    contact.name = name
                    contact.raw_data = contact_data
                    contact.synced_at = timezone.now()  # Update sync time
                    contact.save()
                    updated_count += 1

                if contact.is_matched:
                    matched_count += 1

            except Exception as e:
                errors.append(f"Error processing {contact_data}: {str(e)}")

        return {
            'created': created_count,
            'updated': updated_count,
            'matched': matched_count,
            'errors': errors,
            'total_processed': created_count + updated_count
        }

