# Walk-Around Images API Documentation

## Overview
The Walk-Around Images API allows authenticated customers and admin customer care users to manage walk-around images with the following features:
- Add, view, edit, and remove walk-around images
- Status management (active/inactive)
- Maximum 5 active images per user (automatic deactivation of oldest when limit exceeded)
- User isolation (users can only access their own images)

## Authentication
All endpoints require authentication using JWT tokens. Only users with customer care profiles or student profiles can access these endpoints.

### Authorization Header
```
Authorization: Bearer <your_jwt_token>
```

## Base URL
```
http://localhost:8000/api/customrcare/
```

## Endpoints

### 1. List and Create Walk-Around Images
**Endpoint:** `GET/POST /walk-around-images/`

#### GET - List Images
Lists all walk-around images for the authenticated user.

**Request:**
```http
GET /api/customrcare/walk-around-images/
Authorization: Bearer <token>
```

**Response (200 OK):**
```json
{
  "count": 3,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "user": 1,
      "user_username": "testuser",
      "image": "/media/walk_around_images/image1.jpg",
      "image_url": "http://localhost:8000/media/walk_around_images/image1.jpg",
      "title": "Front View",
      "description": "Front view of the vehicle",
      "status": "active",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### POST - Create Image
Creates a new walk-around image.

**Request:**
```http
POST /api/customrcare/walk-around-images/
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form Data:
- image: [image file]
- title: "Front View"
- description: "  "
- status: "active"
```

**Response (201 Created):**
```json
{
  "id": 1,
  "user": 1,
  "user_username": "testuser",
  "image": "/media/walk_around_images/image1.jpg",
  "image_url": "http://localhost:8000/media/walk_around_images/image1.jpg",
  "title": "Front View",
  "description": "Front view of the vehicle",
  "status": "active",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 2. Retrieve, Update, Delete Specific Image
**Endpoint:** `GET/PUT/PATCH/DELETE /walk-around-images/{id}/`

#### GET - Retrieve Image
**Request:**
```http
GET /api/customrcare/walk-around-images/1/
Authorization: Bearer <token>
```

#### PUT/PATCH - Update Image
**Request:**
```http
PATCH /api/customrcare/walk-around-images/1/
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Updated Front View",
  "description": "Updated description",
  "status": "inactive"
}
```

#### DELETE - Remove Image
**Request:**
```http
DELETE /api/customrcare/walk-around-images/1/
Authorization: Bearer <token>
```

**Response (200 OK):**
```json
{
  "message": "Walk-around image \"Front View\" (ID: 1) has been successfully deleted.",
  "deleted_image_id": 1,
  "deleted_image_title": "Front View"
}
```

### 3. Update Image Status
**Endpoint:** `PATCH /walk-around-images/{id}/status/`

Dedicated endpoint for updating only the status of an image.

**Request:**
```http
PATCH /api/customrcare/walk-around-images/1/status/
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "inactive"
}
```

**Response (200 OK):**
```json
{
  "id": 1,
  "user": 1,
  "user_username": "testuser",
  "image": "/media/walk_around_images/image1.jpg",
  "image_url": "http://localhost:8000/media/walk_around_images/image1.jpg",
  "title": "Front View",
  "description": "Front view of the vehicle",
  "status": "inactive",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:35:00Z"
}
```

### 4. Get Statistics
**Endpoint:** `GET /walk-around-images/stats/`

Returns statistics about the user's walk-around images.

**Request:**
```http
GET /api/customrcare/walk-around-images/stats/
Authorization: Bearer <token>
```

**Response (200 OK):**
```json
{
  "total_images": 7,
  "active_images": 5,
  "inactive_images": 2,
  "max_active_allowed": 5,
  "can_add_more_active": false
}
```

## Business Rules

### Active Image Limit
- Maximum 5 active images per user
- When creating/updating an image to active status and the limit is exceeded:
  - The oldest active image is automatically set to inactive
  - The new/updated image becomes active

### Status Values
- `active`: Image is currently active and counts toward the 5-image limit
- `inactive`: Image is deactivated and doesn't count toward the limit

### User Isolation
- Users can only view, create, update, and delete their own images
- No cross-user access is allowed

## Error Responses

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 403 Forbidden
```json
{
  "detail": "You do not have permission for this resource."
}
```

### 400 Bad Request
```json
{
  "status": ["Status must be either 'active' or 'inactive'."]
}
```

### 404 Not Found
```json
{
  "detail": "Not found."
}
```

## Testing with cURL

### 1. Login to get token
```bash
# For customer care user
curl -X POST http://localhost:8000/api/customrcare/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'

# For student user
curl -X POST http://localhost:8000/api/students/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'
```

### 2. Create walk-around image
```bash
curl -X POST http://localhost:8000/api/customrcare/walk-around-images/ \
  -H "Authorization: Bearer <your_token>" \
  -F "image=@/path/to/your/image.jpg" \
  -F "title=Front View" \
  -F "description=Front view of vehicle" \
  -F "status=active"
```

### 3. List images
```bash
curl -X GET http://localhost:8000/api/customrcare/walk-around-images/ \
  -H "Authorization: Bearer <your_token>"
```

### 4. Update image status
```bash
curl -X PATCH http://localhost:8000/api/customrcare/walk-around-images/1/status/ \
  -H "Authorization: Bearer <your_token>" \
  -H "Content-Type: application/json" \
  -d '{"status": "inactive"}'
```

### 5. Delete image
```bash
curl -X DELETE http://localhost:8000/api/customrcare/walk-around-images/1/ \
  -H "Authorization: Bearer <your_token>"
```

### 6. Get statistics
```bash
curl -X GET http://localhost:8000/api/customrcare/walk-around-images/stats/ \
  -H "Authorization: Bearer <your_token>"
```

## Notes
- All image uploads are stored in the `media/walk_around_images/` directory
- Image URLs are returned as absolute URLs in API responses
- The API supports common image formats (JPEG, PNG, etc.)
- File size limits apply as configured in Django settings
