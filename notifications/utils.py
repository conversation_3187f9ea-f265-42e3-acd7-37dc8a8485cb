from firebase_admin import messaging

def send_fcm_notification(registration_tokens, title, body, data=None):
    """
    Send FCM notification to specified tokens
    
    :param registration_tokens: List of device tokens
    :param title: Notification title
    :param body: Notification body
    :param data: Optional additional data
    :return: FCM message response
    """
    try:
        # Validate inputs
        if not registration_tokens:
            print("No registration tokens provided")
            return None

        # Prepare notification
        notification = messaging.Notification(
            title=title,
            body=body
        )

        # Prepare message
        message = messaging.MulticastMessage(
            notification=notification,
            tokens=registration_tokens,
            data=data or {}  # Ensure data is a dictionary
        )
        
        # Send a message to the devices corresponding to the provided registration tokens
        response = messaging.send_multicast(message)
        
        # Detailed logging of message sending
        print(f'Successfully sent {response.success_count} messages')
        if response.failure_count > 0:
            for resp in response.responses:
                if not resp.success:
                    print(f'Failed to send message: {resp.exception}')
        
        return response
    except Exception as e:
        print(f"Error sending FCM notification: {str(e)}")
        return None