from django.contrib import admin
from django.conf import settings
from django.utils import timezone
import razorpay

from .models import *

# Register your models here.

admin.site.register(Wallet)
admin.site.register(Transaction)

# client = razorpay.Client(auth=(settings.RAZORPAY_KEY_ID, settings.RAZORPAY_KEY_SECRET))

# def approve_withdrawal(request_id):
#     withdrawal = WithdrawalRequest.objects.get(id=request_id)

#     if withdrawal.status != "Pending":
#         return "Already processed"

#     wallet = withdrawal.wallet

#     if wallet.balance < withdrawal.amount:
#         return "Insufficient balance"

#     # Try Razorpay fund transfer to UPI
#     try:
#         transfer = client.payouts.create({
#             "account_number": YOUR_RAZORPAY_ACCOUNT_NUMBER,
#             "fund_account": {
#                 "account_type": "vpa",
#                 "vpa": {
#                     "address": withdrawal.upi_id
#                 },
#                 "contact": {
#                     "name": wallet.user.get_full_name(),
#                     "type": "vendor",
#                     "email": wallet.user.email,
#                 }
#             },
#             "amount": withdrawal.amount * 100,
#             "currency": "INR",
#             "mode": "UPI",
#             "purpose": "payout",
#             "queue_if_low_balance": True
#         })

#         # Mark transaction & wallet update
#         wallet.balance -= withdrawal.amount
#         wallet.save()

#         txn = Transaction.objects.create(
#             wallet=wallet,
#             amount=withdrawal.amount,
#             is_debit=True,
#             status="Success",
#             note="Withdrawal approved",
#             transaction_id=transfer["id"]
#         )

#         withdrawal.status = "Approved"
#         withdrawal.approved_at = timezone.now()
#         withdrawal.transaction = txn
#         withdrawal.save()

#         return "Approved successfully"

#     except Exception as e:
#         return f"Error: {str(e)}"
