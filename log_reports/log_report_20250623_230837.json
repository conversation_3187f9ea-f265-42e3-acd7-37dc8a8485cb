{"metadata": {"generated_at": "2025-06-23T17:38:37.811258+00:00", "period_start": "2025-06-22T17:38:37.811258+00:00", "period_end": "2025-06-23T17:38:37.811258+00:00", "period_days": 1, "sections_included": ["all"]}, "error_summary": {"total_errors": 30, "by_type": [{"error_type": "BUSINESS_LOGIC", "count": 4}, {"error_type": "FRONTEND", "count": 23}, {"error_type": "VALIDATION", "count": 3}], "by_severity": [{"severity": "CRITICAL", "count": 4}, {"severity": "HIGH", "count": 8}, {"severity": "LOW", "count": 3}, {"severity": "MEDIUM", "count": 15}], "unresolved": 30, "critical_errors": 4}, "error_details": [{"timestamp": "2025-06-23T17:38:36.180065+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2_ffa29745", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:38:36.017441+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser_18c7764a", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:22:59.925354+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Old test error", "user": "cleanup_admin", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.829313+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.822551+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.814300+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.807662+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.799835+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.593762+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: Test API error logging", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.535352+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.527686+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.521208+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.512769+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.505911+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.632375+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.625826+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.619070+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.611164+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.604410+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.406063+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: Test API error logging", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.530202+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.523289+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.515608+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.507975+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.500796+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.297203+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: Test API error logging", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T16:22:00.489102+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2_a8e888e1", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:22:00.327874+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser_1e65985f", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:19:34.405300+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:19:34.243859+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser", "path": "", "resolved": false}], "performance_summary": {"total_requests": 72, "avg_response_time": 0.14844305316607156, "max_response_time": 1.5, "min_response_time": 0.0005171298980712891, "slow_requests": 0, "avg_db_queries": 5.347222222222222, "by_status_code": [{"status_code": 200, "count": 34}, {"status_code": 201, "count": 23}, {"status_code": 401, "count": 3}, {"status_code": 403, "count": 2}, {"status_code": 404, "count": 10}]}, "performance_details": [{"timestamp": "2025-06-23T17:22:59.922623+00:00", "path": "/test/old", "method": "GET", "duration": 1.5, "status_code": 200, "db_queries": 0, "user": "cleanup_admin"}, {"timestamp": "2025-06-23T17:23:00.782881+00:00", "path": "/test/command", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "command_admin"}, {"timestamp": "2025-06-23T17:23:00.620281+00:00", "path": "/test/2", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin"}, {"timestamp": "2025-06-23T17:23:00.619273+00:00", "path": "/test/1", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin"}, {"timestamp": "2025-06-23T17:23:00.618081+00:00", "path": "/test/0", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin"}, {"timestamp": "2025-06-23T17:23:00.453897+00:00", "path": "/api/log-admin/cleanup/", "method": "GET", "duration": 0.5053298473358154, "status_code": 200, "db_queries": 17, "user": "cleanup_admin"}, {"timestamp": "2025-06-23T17:38:36.016139+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser_18c7764a"}, {"timestamp": "2025-06-23T17:22:59.926853+00:00", "path": "/test/recent", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "cleanup_admin"}, {"timestamp": "2025-06-23T16:22:00.326542+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser_1e65985f"}, {"timestamp": "2025-06-23T16:19:34.242276+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser"}, {"timestamp": "2025-06-23T16:22:01.109262+00:00", "path": "/api/log-admin/health/", "method": "GET", "duration": 0.45415616035461426, "status_code": 200, "db_queries": 4, "user": "admin_03676461"}, {"timestamp": "2025-06-23T17:38:36.714293+00:00", "path": "/api/log-admin/health/", "method": "GET", "duration": 0.3679940700531006, "status_code": 200, "db_queries": 4, "user": "admin_71f3ea52"}, {"timestamp": "2025-06-23T17:07:06.487766+00:00", "path": "/api/customrcare/error-resolution/1435/", "method": "POST", "duration": 0.3426797389984131, "status_code": 200, "db_queries": 8, "user": "frontend_admin_c062c4e1"}, {"timestamp": "2025-06-23T17:04:13.299396+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.3423011302947998, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:02:56.123150+00:00", "path": "/customrcare/log-error/", "method": "POST", "duration": 0.3387470245361328, "status_code": 404, "db_queries": 0, "user": null}, {"timestamp": "2025-06-23T17:07:29.595761+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.3210427761077881, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:05:29.407770+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.24329710006713867, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:07:29.624618+00:00", "path": "/api/customrcare/error-analytics/", "method": "GET", "duration": 0.013545036315917969, "status_code": 200, "db_queries": 20, "user": "api_test_user_afb53e84"}, {"timestamp": "2025-06-23T17:05:29.428184+00:00", "path": "/api/customrcare/error-analytics/", "method": "GET", "duration": 0.012538909912109375, "status_code": 200, "db_queries": 18, "user": "api_test_user_57847d33"}, {"timestamp": "2025-06-23T17:23:00.634364+00:00", "path": "/api/log-admin/retention/", "method": "GET", "duration": 0.012281179428100586, "status_code": 200, "db_queries": 25, "user": "retention_policy_admin"}, {"timestamp": "2025-06-23T17:38:36.749614+00:00", "path": "/api/log-admin/analytics/", "method": "GET", "duration": 0.00987696647644043, "status_code": 200, "db_queries": 24, "user": "admin_71f3ea52"}, {"timestamp": "2025-06-23T16:22:01.138394+00:00", "path": "/api/log-admin/analytics/", "method": "GET", "duration": 0.00956106185913086, "status_code": 200, "db_queries": 24, "user": "admin_03676461"}, {"timestamp": "2025-06-23T17:38:36.737208+00:00", "path": "/api/log-admin/errors/", "method": "GET", "duration": 0.008874893188476562, "status_code": 200, "db_queries": 3, "user": "admin_71f3ea52"}, {"timestamp": "2025-06-23T17:38:36.725452+00:00", "path": "/api/log-admin/performance/", "method": "GET", "duration": 0.008615970611572266, "status_code": 200, "db_queries": 3, "user": "admin_71f3ea52"}, {"timestamp": "2025-06-23T17:07:29.607639+00:00", "path": "/api/customrcare/get-errors/", "method": "GET", "duration": 0.008298158645629883, "status_code": 200, "db_queries": 10, "user": "api_test_user_afb53e84"}, {"timestamp": "2025-06-23T17:05:29.587501+00:00", "path": "/api/customrcare/error-resolution/1432/", "method": "POST", "duration": 0.006929874420166016, "status_code": 200, "db_queries": 8, "user": "frontend_admin_a2b7dd19"}, {"timestamp": "2025-06-23T16:22:01.118722+00:00", "path": "/api/log-admin/performance/", "method": "GET", "duration": 0.006453990936279297, "status_code": 200, "db_queries": 3, "user": "admin_03676461"}, {"timestamp": "2025-06-23T17:04:13.502465+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.0063970088958740234, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:07:29.783680+00:00", "path": "/api/customrcare/error-resolution/1438/", "method": "POST", "duration": 0.005991935729980469, "status_code": 200, "db_queries": 8, "user": "frontend_admin_f3689644"}, {"timestamp": "2025-06-23T17:07:06.522361+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.005611896514892578, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:38:36.757597+00:00", "path": "/api/log-admin/dashboard/", "method": "GET", "duration": 0.005589008331298828, "status_code": 200, "db_queries": 14, "user": "admin_71f3ea52"}, {"timestamp": "2025-06-23T17:07:29.801603+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.005448818206787109, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:05:29.605738+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.005248069763183594, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:05:29.620389+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.005232334136962891, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:07:06.507433+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.005039215087890625, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T16:22:01.146269+00:00", "path": "/api/log-admin/dashboard/", "method": "GET", "duration": 0.004803895950317383, "status_code": 200, "db_queries": 14, "user": "admin_03676461"}, {"timestamp": "2025-06-23T17:38:36.764864+00:00", "path": "/api/log-admin/config/", "method": "GET", "duration": 0.004727840423583984, "status_code": 200, "db_queries": 2, "user": "admin_71f3ea52"}, {"timestamp": "2025-06-23T17:05:29.634258+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.0046918392181396484, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:02:56.137174+00:00", "path": "/customrcare/error-analytics/", "method": "GET", "duration": 0.004680156707763672, "status_code": 404, "db_queries": 0, "user": null}, {"timestamp": "2025-06-23T17:07:06.536510+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.004616737365722656, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T16:22:01.126096+00:00", "path": "/api/log-admin/errors/", "method": "GET", "duration": 0.0045511722564697266, "status_code": 200, "db_queries": 3, "user": "admin_03676461"}, {"timestamp": "2025-06-23T17:04:13.517209+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.004533052444458008, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:07:29.808889+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.0044858455657958984, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:07:29.823851+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.004472255706787109, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:05:29.612384+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.004337787628173828, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:23:00.461686+00:00", "path": "/api/log-admin/cleanup/", "method": "POST", "duration": 0.004324913024902344, "status_code": 200, "db_queries": 9, "user": "cleanup_admin"}, {"timestamp": "2025-06-23T17:07:06.514033+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.00423884391784668, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:07:29.815575+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.004238128662109375, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:07:29.830469+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.0042018890380859375, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:04:13.509245+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.00417780876159668, "status_code": 201, "db_queries": 6, "user": null}], "activity_summary": {"total_activities": 16, "unique_users": 10, "by_type": [{"activity_type": "API_CALL", "count": 9}, {"activity_type": "LOGIN", "count": 3}, {"activity_type": "OTHER", "count": 4}], "successful_activities": 16, "failed_activities": 0}, "activity_details": [{"timestamp": "2025-06-23T17:38:36.763060+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/config/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.756371+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/dashboard/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.748286+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/analytics/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.735881+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/errors/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.724033+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/performance/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.712827+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/health/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.175689+00:00", "user": "testuser2_ffa29745", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T17:38:36.018567+00:00", "user": "testuser_18c7764a", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T16:22:00.484893+00:00", "user": "testuser2_a8e888e1", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T16:22:00.330524+00:00", "user": "testuser_1e65985f", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T16:19:34.400831+00:00", "user": "testuser2", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T16:19:34.245827+00:00", "user": "testuser", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T14:49:28.273577+00:00", "user": "<PERSON><PERSON><PERSON>", "activity_type": "OTHER", "action": "Contributor <PERSON><PERSON>", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T10:28:25.283082+00:00", "user": "nayan", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}, {"timestamp": "2025-06-22T20:46:03.453611+00:00", "user": "nayan", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}, {"timestamp": "2025-06-22T20:42:56.267666+00:00", "user": "testlogin", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}], "security_summary": {"total_incidents": 3, "by_type": [{"incident_type": "BRUTE_FORCE", "count": 3}], "by_severity": [{"severity": "MEDIUM", "count": 3}], "unresolved": 3, "critical_incidents": 0, "blocked_incidents": 0}, "security_details": [{"timestamp": "2025-06-23T17:38:36.024605+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}, {"timestamp": "2025-06-23T16:22:00.335627+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}, {"timestamp": "2025-06-23T16:19:34.251806+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}]}