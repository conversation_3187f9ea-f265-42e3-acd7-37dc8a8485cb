# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Django
*.log
*.pot
*.pyc
db.sqlite3
test_db.sqlite3
media/
migrations/

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
ENV/
env/
.env
.venv
env.bak/
venv.bak/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Django migrations
# Uncomment if you want to ignore migrations
# */migrations/*.py
# !*/migrations/__init__.py

# Local development settings
local_settings.py

# Secret keys and sensitive information
serviceAccountKey.json
*.pem
*.key

# Logs
logs/
*.log

# Test coverage
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# Translations
*.mo

# Documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# Temporary files
*.bak
*.tmp
*~
shashtrarth/settings.py

staticfiles/
customrcare/__pycache__/urls.cpython-311.pyc
