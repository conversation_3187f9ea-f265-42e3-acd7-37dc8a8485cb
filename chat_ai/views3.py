from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.apps import apps
import google.generativeai as genai
import ast  # safer alternative to eval

# Gemini setup
genai.configure(api_key='AIzaSyD2y8WM03f5fBDIo2uPfO-r8FiKkYuO7hQ')
gemini_model = genai.GenerativeModel('gemini-2.0-flash')

def get_all_model_metadata():
    metadata = {}
    for model in apps.get_models():
        model_name = model.__name__
        field_names = [field.name for field in model._meta.fields]
        keywords = [model_name.lower(), model_name.lower() + 's'] + [f.lower() for f in field_names]
        metadata[model_name] = {
            'model': model,
            'fields': field_names,
            'keywords': keywords
        }
    return metadata

def find_best_matching_model(query, metadata):
    query_lower = query.lower()
    scores = []

    for name, meta in metadata.items():
        score = 0

        # Boost score if model name or its plural is directly mentioned
        if name.lower() in query_lower or (name.lower() + 's') in query_lower:
            score += 5

        # Add points for field keyword matches
        score += sum(1 for kw in meta['keywords'] if kw in query_lower)

        if score > 0:
            scores.append((score, meta))

    if scores:
        scores.sort(key=lambda x: x[0], reverse=True)
        return scores[0][1]
    return None

class AIChatbotAPIView(APIView):
    def post(self, request):
        query = request.data.get('query', '')
        if not query:
            return Response({'error': 'Query not provided'}, status=status.HTTP_400_BAD_REQUEST)

        model_metadata = get_all_model_metadata()

        # Match model from query
        matched_model = find_best_matching_model(query, model_metadata)
        if not matched_model:
            return Response({'error': 'No matching model found'}, status=status.HTTP_404_NOT_FOUND)

        model_class = matched_model['model']
        fields = matched_model['fields']

        # Step 1: Try generating filters from Gemini
        filter_prompt = f"""
        You are a Django ORM expert.

        User's query: "{query}"
        Model: {model_class.__name__}
        Available fields: {fields}

        Based on the user's query, generate a valid Python dictionary for Django ORM filtering.
        Example: {{'difficulty': 2, 'status': 'active'}}

        Only return the dictionary. Do NOT include any explanation or extra text.
        """
        filter_response = gemini_model.generate_content(filter_prompt)

        try:
            filters = ast.literal_eval(filter_response.text.strip())
        except Exception as e:
            filters = {}

        # Step 2: Apply filters
        try:
            data_qs = model_class.objects.filter(**filters)[:5]
        except:
            data_qs = model_class.objects.all()[:5]

        data_list = list(data_qs.values(*fields))

        # Step 3: Explain data
        # explanation_prompt = f"""
        # You are an AI assistant helping explain database records to a student.

        # User asked: '{query}'

        # Model name: {model_class.__name__}

        # Here is the data from the database (JSON format):
        # {data_list}

        # Please explain what this data represents and highlight the important fields in a simple, student-friendly way.
        # """

        explanation_prompt = f"""
                You are a friendly AI assistant talking to a student in a casual and helpful tone.

                The student asked: "{query}"

                You found some data from the "{model_class.__name__}" model. Here's the data in JSON format:
                {data_list}

                Please talk to the student like a helpful human. Explain what this data means in a friendly and simple way. Point out important fields (like name, id, etc.) and also give them a useful link to learn more or explore this on the website.

                Assume each object may have a `slug` field which can be used to build a URL like: https://example.com/{model_class.__name__.lower()}s/{{slug}}

                If there's no slug, you can link to the main listing page: https://example.com/{model_class.__name__.lower()}s/

                End your explanation with something natural like “Let me know if you want more info!”

                Only return the final message as if you’re chatting directly with the student.
                """

        gemini_response = gemini_model.generate_content(explanation_prompt)
        explanation = gemini_response.text.strip()

        return Response({
            'model': model_class.__name__,
            'filters_applied': filters,
            'data': data_list,
            'explanation': explanation,
        })
