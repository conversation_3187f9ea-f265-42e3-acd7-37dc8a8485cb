from rest_framework import serializers
from .models import FCMDevice, Notification, NotificationImage

class FCMDeviceSerializer(serializers.ModelSerializer):
    interest = serializers.JSONField(required=False)
    registered_courses = serializers.JSONField(required=False)
    class Meta:
        model = FCMDevice
        fields = ['id','user', 'registration_token', 'device_type', "interest", "registered_courses", "is_registered", "site", "created_at", "is_active"]
        read_only_fields = ['created_at', 'is_active']
        extra_kwargs = {
            'registration_token': {'required': True},
            'device_type': {'required': True},
            'interest': {'required': False, 'default': list},
            'registered_courses': {'required': False, 'default': list},
          
        }
    def validate_is_registered(self, value):
        if self.instance and self.instance.is_registered and not value:
            raise serializers.ValidationError("Once registered, the device cannot be unregistered.")
        return value


import json

class NotificationSerializer(serializers.ModelSerializer):
    notification = serializers.SerializerMethodField()
    data = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        fields = ['id', 'notification', 'data', 'created_at']

    def get_notification(self, obj):
        request = self.context.get('request')
        return {
            "title": obj.n_title,
            "body": obj.n_body,
            "image": request.build_absolute_uri(obj.n_image.url) if obj.n_image else None,
            "description": obj.n_description,
            "icon": obj.icon,
        }

    def get_data(self, obj):
        request = self.context.get('request')
        return {
            "title": obj.d_title,
            "body": obj.d_body,
            "image": request.build_absolute_uri(obj.n_image.url) if obj.n_image else None,
            "description": obj.d_description,
            "d_url" :obj.d_url,
        }

    def create(self, validated_data):
        request = self.context['request']

        # 🔹 Parse JSON strings into dictionaries
        try:
            notification_data = json.loads(request.data.get('notification', '{}'))
            data_data = json.loads(request.data.get('data', '{}'))
        except json.JSONDecodeError:
            notification_data = {}
            data_data = {}

        # 🔹 Get images from `request.FILES`
        n_image = request.FILES.get('n_image', None)
        d_image = request.FILES.get('d_image', None)

        notification = Notification.objects.create(
            n_title=notification_data.get('title', ''),
            n_body=notification_data.get('body', ''),
            n_image=n_image,  # ✅ Handles file uploads correctly
            n_description=notification_data.get('description', ''),

            d_title=data_data.get('title', ''),
            d_body=data_data.get('body', ''),
            d_image=d_image,  # ✅ Handles file uploads correctly
            d_description=data_data.get('description', '')
        )

        return notification

class NotificationImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationImage
        fields = ['id', 'image']

    