from rest_framework import serializers
from django.contrib.auth.models import User
from django.db import transaction
from .models import Contact, normalize_phone_number
from students.models import Student
import logging

logger = logging.getLogger(__name__)


class ContactDataSerializer(serializers.Serializer):
    """Serializer for individual contact data from Android app"""
    name = serializers.CharField(max_length=255, required=False, allow_blank=True)
    contact = serializers.CharField(max_length=20)

    def validate_contact(self, value):
        """Basic validation - detailed validation happens in sync function"""
        if not value:
            raise serializers.ValidationError("Contact number is required")

        # Just return the original value - let the sync function handle detailed validation
        # This allows us to collect all validation errors and provide better reporting
        return value


class ContactSyncSerializer(serializers.Serializer):
    """Serializer for syncing contacts from Android app"""
    contacts = ContactDataSerializer(many=True)

    def validate_contacts(self, value):
        """Validate the contacts list"""
        if not value:
            raise serializers.ValidationError("Contacts list cannot be empty")

        if len(value) > 2000:  # Increased limit for phone contacts
            raise serializers.ValidationError("Cannot sync more than 2000 contacts at once")

        return value

    def create(self, validated_data):
        """Sync contacts using the streamlined approach"""
        user = self.context['request'].user
        contacts_data = validated_data['contacts']

        # Use the model's sync method
        result = Contact.sync_contacts_for_user(user, contacts_data)

        logger.info(f"User {user.username} synced contacts: {result}")

        return result


class ContactSerializer(serializers.ModelSerializer):
    """Serializer for the streamlined Contact model"""
    related_user_info = serializers.SerializerMethodField()

    class Meta:
        model = Contact
        fields = [
            'id', 'name', 'contact_number', 'is_matched',
            'related_user_info', 'synced_at', 'updated_at'
        ]
        read_only_fields = ['id', 'is_matched', 'related_user_info', 'synced_at', 'updated_at']

    def get_related_user_info(self, obj):
        """Get basic info about related user if matched"""
        if obj.related_user:
            try:
                student = obj.related_user.student_profile
                return {
                    'username': obj.related_user.username,
                    'first_name': obj.related_user.first_name,
                    'last_name': obj.related_user.last_name,
                    'student_id': student.student_id,
                    'image_url': student.image_url if hasattr(student, 'image_url') else None
                }
            except Student.DoesNotExist:
                return {
                    'username': obj.related_user.username,
                    'first_name': obj.related_user.first_name,
                    'last_name': obj.related_user.last_name
                }
        return None


class ContactStatsSerializer(serializers.Serializer):
    """Serializer for contact statistics"""
    total_synced = serializers.IntegerField()
    total_matched = serializers.IntegerField()
    total_unmatched = serializers.IntegerField()
    last_sync = serializers.DateTimeField()


class ContactSearchSerializer(serializers.Serializer):
    """Serializer for contact search functionality"""
    query = serializers.CharField(max_length=255, required=False)
    contact_number = serializers.CharField(max_length=15, required=False)
    is_matched = serializers.BooleanField(required=False)

    def validate(self, data):
        """Ensure at least one search parameter is provided"""
        if not any(data.values()):
            raise serializers.ValidationError("At least one search parameter must be provided")
        return data
