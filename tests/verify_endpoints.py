#!/usr/bin/env python3
"""
Endpoint verification script to ensure all URLs are working
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_endpoint_exists(url, method="GET", headers=None, data=None):
    """Test if an endpoint exists (not 404)"""
    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 404:
            return False, f"404 Not Found"
        else:
            return True, f"{response.status_code}"
    except Exception as e:
        return False, f"Error: {str(e)}"

def main():
    print("🔍 Verifying Contact Management Endpoints")
    print("=" * 50)
    
    # Test endpoints without authentication (should get 401, not 404)
    endpoints = [
        ("POST", "/api/contacts/sync/", "Contact Sync"),
        ("GET", "/api/contacts/my-contacts/", "My Contacts"),
        ("GET", "/api/contacts/matched/", "Matched Contacts"),
        ("GET", "/api/contacts/stats/", "Contact Stats"),
        ("GET", "/api/contacts/search/", "Search Contacts"),
        ("GET", "/api/contacts/admin/contacts/", "Admin Contacts"),
    ]
    
    all_good = True
    
    for method, endpoint, name in endpoints:
        url = f"{BASE_URL}{endpoint}"
        exists, status = test_endpoint_exists(url, method)
        
        if exists:
            print(f"✅ {name}: {endpoint} - {status}")
        else:
            print(f"❌ {name}: {endpoint} - {status}")
            all_good = False
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("✅ All endpoints are accessible!")
        print("\nNote: 401/403 responses are expected without authentication.")
        print("404 responses would indicate missing endpoints.")
    else:
        print("❌ Some endpoints are missing or not configured properly!")
        return 1
    
    # Test old endpoints (should return 404)
    print("\n🔍 Checking old endpoints (should be 404):")
    old_endpoints = [
        ("POST", "/api/contacts/upload/", "Old Upload"),
        ("GET", "/api/contacts/relationships/", "Old Relationships"),
        ("GET", "/api/contacts/mutual/", "Old Mutual"),
    ]
    
    for method, endpoint, name in old_endpoints:
        url = f"{BASE_URL}{endpoint}"
        exists, status = test_endpoint_exists(url, method)
        
        if not exists and "404" in status:
            print(f"✅ {name}: {endpoint} - Correctly removed (404)")
        else:
            print(f"⚠️ {name}: {endpoint} - Still exists ({status})")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
