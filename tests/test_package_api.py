#!/usr/bin/env python3
"""
Test script to check package purchase API endpoints
"""
import requests
import json
import time

BASE_URL = "http://127.0.0.1:8000/api/packages"

def test_server_connection():
    """Test if server is responding"""
    print("Testing server connection...")
    try:
        response = requests.get("http://127.0.0.1:8000/", timeout=5)
        print(f"Server responding: {response.status_code}")
        return True
    except Exception as e:
        print(f"Server connection failed: {e}")
        return False

def test_package_list():
    """Test package listing endpoint"""
    print("\nTesting package listing...")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Found {len(data)} packages")
            for pkg in data[:3]:  # Show first 3 packages
                print(f"- {pkg['name']} ({pkg['package_type']}) - ₹{pkg['discount_price']}")
            return True
        else:
            print(f"Error: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"Request failed: {e}")
        return False

def test_razorpay_config():
    """Test Razorpay config endpoint"""
    print("\nTesting Razorpay config...")
    try:
        response = requests.get(f"{BASE_URL}/razorpay-config/", timeout=10)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Razorpay Key: {data.get('razorpay_key', 'Not found')}")
            return True
        else:
            print(f"Error: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"Request failed: {e}")
        return False

def test_new_subscription_creation():
    """Test new subscription creation endpoint (v2)"""
    print("\nTesting new subscription creation (v2)...")

    # Test data
    data = {
        "student": 11,  # Using student ID from our shell test
        "package": 1    # Using package ID from our shell test
    }

    try:
        response = requests.post(f"{BASE_URL}/v2/create-subscription/", json=data, timeout=15)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 201:
            result = response.json()
            print("SUCCESS! Subscription created:")
            print(f"- Subscription ID: {result.get('subscription_id')}")
            print(f"- Final Price: ₹{result.get('final_price')}")
            print(f"- Razorpay Order ID: {result.get('razorpay_order_id', 'N/A')}")
            print(f"- Is Free: {result.get('is_free')}")
            return True
        else:
            print(f"Error Response: {response.text[:300]}")
            return False
    except Exception as e:
        print(f"Request failed: {e}")
        return False

def test_subscription_creation():
    """Test old subscription creation endpoint"""
    print("\nTesting old subscription creation...")

    # Test data
    data = {
        "student": 11,  # Using student ID from our shell test
        "package": 1    # Using package ID from our shell test
    }

    try:
        response = requests.post(f"{BASE_URL}/subscriptions/", json=data, timeout=15)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("SUCCESS! Subscription created:")
            print(f"- Subscription ID: {result.get('subscription_id')}")
            print(f"- Final Price: ₹{result.get('final_price')}")
            print(f"- Razorpay Order: {result.get('razorpay_order', {}).get('id', 'N/A')}")
            return True
        else:
            print(f"Error Response: {response.text[:300]}")
            return False
    except Exception as e:
        print(f"Request failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing Package Purchase API Endpoints")
    print("=" * 50)

    # Test server connection first
    if not test_server_connection():
        print("Server is not responding. Please check if Django server is running.")
        exit(1)

    # Test endpoints
    success_count = 0
    total_tests = 4

    if test_package_list():
        success_count += 1

    if test_razorpay_config():
        success_count += 1

    if test_new_subscription_creation():
        success_count += 1

    if test_subscription_creation():
        success_count += 1

    print(f"\n{'='*50}")
    print(f"Test Results: {success_count}/{total_tests} tests passed")

    if success_count == total_tests:
        print("🎉 All tests passed! Package purchase API is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
