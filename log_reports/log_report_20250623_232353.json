{"metadata": {"generated_at": "2025-06-23T17:53:53.801824+00:00", "period_start": "2025-06-22T17:53:53.801824+00:00", "period_end": "2025-06-23T17:53:53.801824+00:00", "period_days": 1, "sections_included": ["all"]}, "error_summary": {"total_errors": 41, "by_type": [{"error_type": "BUSINESS_LOGIC", "count": 8}, {"error_type": "FRONTEND", "count": 29}, {"error_type": "VALIDATION", "count": 4}], "by_severity": [{"severity": "CRITICAL", "count": 5}, {"severity": "HIGH", "count": 10}, {"severity": "LOW", "count": 4}, {"severity": "MEDIUM", "count": 22}], "unresolved": 41, "critical_errors": 5}, "error_details": [{"timestamp": "2025-06-23T17:53:52.178345+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2_4a4ca28b", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:53:52.017844+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser_add19ca6", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:47:56.067817+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Old test error", "user": "cleanup_test_admin", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:46:19.351337+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Old test error", "user": "cleanup_admin_44f5cba2", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:44:50.590497+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Old test error", "user": "cleanup_admin_15ed3492", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:38:47.171392+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:38:47.165520+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:38:47.159595+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:38:47.153143+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:38:47.147206+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:38:46.944807+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: Test API error logging", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:38:36.180065+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2_ffa29745", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:38:36.017441+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser_18c7764a", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:22:59.925354+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Old test error", "user": "cleanup_admin", "path": "", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.829313+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.822551+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.814300+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.807662+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.799835+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:29.593762+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: Test API error logging", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.535352+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.527686+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.521208+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.512769+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:07:06.505911+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.632375+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.625826+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.619070+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.611164+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.604410+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:05:29.406063+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: Test API error logging", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.530202+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "CRITICAL", "message": "Frontend JAVASCRIPT: Critical system failure detected", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.523289+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend TIMEOUT: Request timeout: Server did not respond", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.515608+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend PERMISSION: Permission denied: Access forbidden", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.507975+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "HIGH", "message": "Frontend AUTHENTICATION: Authentication failed: Invalid token", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.500796+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: NetworkError: Failed to fetch data from API", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T17:04:13.297203+00:00", "view_name": "frontend_error", "error_type": "FRONTEND", "severity": "MEDIUM", "message": "Frontend NETWORK: Test API error logging", "user": null, "path": "/api/customrcare/log-error/", "resolved": false}, {"timestamp": "2025-06-23T16:22:00.489102+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2_a8e888e1", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:22:00.327874+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser_1e65985f", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:19:34.405300+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:19:34.243859+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser", "path": "", "resolved": false}], "performance_summary": {"total_requests": 139, "avg_response_time": 0.21837535007394476, "max_response_time": 2.0, "min_response_time": 0.0004749298095703125, "slow_requests": 0, "avg_db_queries": 6.338129496402877, "by_status_code": [{"status_code": 200, "count": 90}, {"status_code": 201, "count": 29}, {"status_code": 401, "count": 6}, {"status_code": 403, "count": 2}, {"status_code": 404, "count": 12}]}, "performance_details": [{"timestamp": "2025-06-23T17:47:56.066681+00:00", "path": "/test/very-old/performance", "method": "GET", "duration": 2.0, "status_code": 200, "db_queries": 0, "user": "cleanup_test_admin"}, {"timestamp": "2025-06-23T17:47:56.064834+00:00", "path": "/test/old/performance", "method": "GET", "duration": 1.5, "status_code": 200, "db_queries": 0, "user": "cleanup_test_admin"}, {"timestamp": "2025-06-23T17:46:19.349444+00:00", "path": "/test/old", "method": "GET", "duration": 1.5, "status_code": 200, "db_queries": 0, "user": "cleanup_admin_44f5cba2"}, {"timestamp": "2025-06-23T17:44:50.588151+00:00", "path": "/test/old", "method": "GET", "duration": 1.5, "status_code": 200, "db_queries": 0, "user": "cleanup_admin_15ed3492"}, {"timestamp": "2025-06-23T17:22:59.922623+00:00", "path": "/test/old", "method": "GET", "duration": 1.5, "status_code": 200, "db_queries": 0, "user": "cleanup_admin"}, {"timestamp": "2025-06-23T17:46:20.064553+00:00", "path": "/test/command", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "command_admin_c0cba23d"}, {"timestamp": "2025-06-23T17:46:19.901974+00:00", "path": "/test/2", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin_bc49ff7f"}, {"timestamp": "2025-06-23T17:46:19.900969+00:00", "path": "/test/1", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin_bc49ff7f"}, {"timestamp": "2025-06-23T17:46:19.899758+00:00", "path": "/test/0", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin_bc49ff7f"}, {"timestamp": "2025-06-23T17:44:51.386348+00:00", "path": "/test/command", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "command_admin_7c95f365"}, {"timestamp": "2025-06-23T17:44:51.223497+00:00", "path": "/test/2", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin_804a5ceb"}, {"timestamp": "2025-06-23T17:44:51.222469+00:00", "path": "/test/1", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin_804a5ceb"}, {"timestamp": "2025-06-23T17:44:51.221229+00:00", "path": "/test/0", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin_804a5ceb"}, {"timestamp": "2025-06-23T17:23:00.782881+00:00", "path": "/test/command", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "command_admin"}, {"timestamp": "2025-06-23T17:23:00.620281+00:00", "path": "/test/2", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin"}, {"timestamp": "2025-06-23T17:23:00.619273+00:00", "path": "/test/1", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin"}, {"timestamp": "2025-06-23T17:23:00.618081+00:00", "path": "/test/0", "method": "GET", "duration": 1.0, "status_code": 200, "db_queries": 0, "user": "retention_policy_admin"}, {"timestamp": "2025-06-23T17:23:00.453897+00:00", "path": "/api/log-admin/cleanup/", "method": "GET", "duration": 0.5053298473358154, "status_code": 200, "db_queries": 17, "user": "cleanup_admin"}, {"timestamp": "2025-06-23T17:53:52.016616+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser_add19ca6"}, {"timestamp": "2025-06-23T17:46:19.352571+00:00", "path": "/test/recent", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "cleanup_admin_44f5cba2"}, {"timestamp": "2025-06-23T17:44:50.591606+00:00", "path": "/test/recent", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "cleanup_admin_15ed3492"}, {"timestamp": "2025-06-23T17:38:36.016139+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser_18c7764a"}, {"timestamp": "2025-06-23T17:22:59.926853+00:00", "path": "/test/recent", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "cleanup_admin"}, {"timestamp": "2025-06-23T16:22:00.326542+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser_1e65985f"}, {"timestamp": "2025-06-23T16:19:34.242276+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser"}, {"timestamp": "2025-06-23T16:22:01.109262+00:00", "path": "/api/log-admin/health/", "method": "GET", "duration": 0.45415616035461426, "status_code": 200, "db_queries": 4, "user": "admin_03676461"}, {"timestamp": "2025-06-23T17:44:51.058224+00:00", "path": "/api/log-admin/cleanup/", "method": "GET", "duration": 0.445065975189209, "status_code": 200, "db_queries": 17, "user": "cleanup_admin_15ed3492"}, {"timestamp": "2025-06-23T17:47:56.499742+00:00", "path": "/api/log-admin/cleanup/", "method": "GET", "duration": 0.404329776763916, "status_code": 200, "db_queries": 17, "user": "cleanup_test_admin"}, {"timestamp": "2025-06-23T17:52:59.084697+00:00", "path": "/api/log-admin/health/", "method": "GET", "duration": 0.40096306800842285, "status_code": 200, "db_queries": 4, "user": "api_test_admin"}, {"timestamp": "2025-06-23T17:53:42.411054+00:00", "path": "/api/log-admin/health/", "method": "GET", "duration": 0.37874484062194824, "status_code": 200, "db_queries": 4, "user": "api_test_admin"}, {"timestamp": "2025-06-23T17:48:59.388187+00:00", "path": "/api/log-admin/cleanup/", "method": "GET", "duration": 0.3735930919647217, "status_code": 200, "db_queries": 17, "user": "cleanup_test_admin"}, {"timestamp": "2025-06-23T17:49:24.005695+00:00", "path": "/api/log-admin/cleanup/", "method": "GET", "duration": 0.3717460632324219, "status_code": 200, "db_queries": 17, "user": "cleanup_test_admin"}, {"timestamp": "2025-06-23T17:38:36.714293+00:00", "path": "/api/log-admin/health/", "method": "GET", "duration": 0.3679940700531006, "status_code": 200, "db_queries": 4, "user": "admin_71f3ea52"}, {"timestamp": "2025-06-23T17:53:52.710955+00:00", "path": "/api/log-admin/health/", "method": "GET", "duration": 0.36714696884155273, "status_code": 200, "db_queries": 4, "user": "admin_2579e648"}, {"timestamp": "2025-06-23T17:46:19.733942+00:00", "path": "/api/log-admin/cleanup/", "method": "GET", "duration": 0.3636600971221924, "status_code": 200, "db_queries": 17, "user": "cleanup_admin_44f5cba2"}, {"timestamp": "2025-06-23T17:07:06.487766+00:00", "path": "/api/customrcare/error-resolution/1435/", "method": "POST", "duration": 0.3426797389984131, "status_code": 200, "db_queries": 8, "user": "frontend_admin_c062c4e1"}, {"timestamp": "2025-06-23T17:04:13.299396+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.3423011302947998, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:02:56.123150+00:00", "path": "/customrcare/log-error/", "method": "POST", "duration": 0.3387470245361328, "status_code": 404, "db_queries": 0, "user": null}, {"timestamp": "2025-06-23T17:07:29.595761+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.3210427761077881, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:38:46.946635+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.30137014389038086, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:05:29.407770+00:00", "path": "/api/customrcare/log-error/", "method": "POST", "duration": 0.24329710006713867, "status_code": 201, "db_queries": 6, "user": null}, {"timestamp": "2025-06-23T17:07:29.624618+00:00", "path": "/api/customrcare/error-analytics/", "method": "GET", "duration": 0.013545036315917969, "status_code": 200, "db_queries": 20, "user": "api_test_user_afb53e84"}, {"timestamp": "2025-06-23T17:38:46.975715+00:00", "path": "/api/customrcare/error-analytics/", "method": "GET", "duration": 0.013114213943481445, "status_code": 200, "db_queries": 21, "user": "api_test_user_26ded9bd"}, {"timestamp": "2025-06-23T17:05:29.428184+00:00", "path": "/api/customrcare/error-analytics/", "method": "GET", "duration": 0.012538909912109375, "status_code": 200, "db_queries": 18, "user": "api_test_user_57847d33"}, {"timestamp": "2025-06-23T17:44:51.237888+00:00", "path": "/api/log-admin/retention/", "method": "GET", "duration": 0.012444257736206055, "status_code": 200, "db_queries": 25, "user": "retention_policy_admin_804a5ceb"}, {"timestamp": "2025-06-23T17:46:19.916061+00:00", "path": "/api/log-admin/retention/", "method": "GET", "duration": 0.012378215789794922, "status_code": 200, "db_queries": 25, "user": "retention_policy_admin_bc49ff7f"}, {"timestamp": "2025-06-23T17:23:00.634364+00:00", "path": "/api/log-admin/retention/", "method": "GET", "duration": 0.012281179428100586, "status_code": 200, "db_queries": 25, "user": "retention_policy_admin"}, {"timestamp": "2025-06-23T17:52:59.108804+00:00", "path": "/api/log-admin/analytics/", "method": "GET", "duration": 0.011421918869018555, "status_code": 200, "db_queries": 24, "user": "api_test_admin"}, {"timestamp": "2025-06-23T17:53:42.434865+00:00", "path": "/api/log-admin/analytics/", "method": "GET", "duration": 0.011057853698730469, "status_code": 200, "db_queries": 24, "user": "api_test_admin"}, {"timestamp": "2025-06-23T17:38:46.959742+00:00", "path": "/api/customrcare/get-errors/", "method": "GET", "duration": 0.010013818740844727, "status_code": 200, "db_queries": 12, "user": "api_test_user_26ded9bd"}], "activity_summary": {"total_activities": 59, "unique_users": 21, "by_type": [{"activity_type": "API_CALL", "count": 51}, {"activity_type": "LOGIN", "count": 4}, {"activity_type": "OTHER", "count": 4}], "successful_activities": 59, "failed_activities": 0}, "activity_details": [{"timestamp": "2025-06-23T17:53:52.759720+00:00", "user": "admin_2579e648", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/config/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:52.753949+00:00", "user": "admin_2579e648", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/dashboard/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:52.745243+00:00", "user": "admin_2579e648", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/analytics/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:52.733817+00:00", "user": "admin_2579e648", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/errors/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:52.722071+00:00", "user": "admin_2579e648", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/performance/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:52.709435+00:00", "user": "admin_2579e648", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/health/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:52.173790+00:00", "user": "testuser2_4a4ca28b", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T17:53:52.019095+00:00", "user": "testuser_add19ca6", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:42.484538+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/health/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:42.474556+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/errors/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:42.463404+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/performance/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:42.451764+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:42.442377+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/config/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:42.433492+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/analytics/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:42.419303+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/dashboard/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:53:42.409250+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/health/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:52:59.154821+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/health/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:52:59.125672+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:52:59.116984+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/config/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:52:59.106819+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/analytics/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:52:59.093067+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/dashboard/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:52:59.082844+00:00", "user": "api_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/health/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:49:24.019880+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:49:24.011981+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:49:24.004068+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:48:59.402920+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:48:59.394307+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:48:59.386561+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:47:56.514373+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:47:56.505613+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:47:56.498136+00:00", "user": "cleanup_test_admin", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:46:19.914711+00:00", "user": "retention_policy_admin_bc49ff7f", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/retention/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:46:19.749280+00:00", "user": "cleanup_admin_44f5cba2", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:46:19.741190+00:00", "user": "cleanup_admin_44f5cba2", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:46:19.732258+00:00", "user": "cleanup_admin_44f5cba2", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:44:51.236399+00:00", "user": "retention_policy_admin_804a5ceb", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/retention/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:44:51.071583+00:00", "user": "cleanup_admin_15ed3492", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:44:51.064866+00:00", "user": "cleanup_admin_15ed3492", "activity_type": "API_CALL", "action": "Created/Submitted to /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:44:51.056602+00:00", "user": "cleanup_admin_15ed3492", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/cleanup/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:47.140708+00:00", "user": "frontend_admin_48b3b703", "activity_type": "API_CALL", "action": "Created/Submitted to /api/customrcare/error-bulk-actions/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:47.131490+00:00", "user": "frontend_admin_48b3b703", "activity_type": "API_CALL", "action": "Created/Submitted to /api/customrcare/error-resolution/1441/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:46.974170+00:00", "user": "api_test_user_26ded9bd", "activity_type": "API_CALL", "action": "Viewed /api/customrcare/error-analytics/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:46.958279+00:00", "user": "api_test_user_26ded9bd", "activity_type": "API_CALL", "action": "Viewed /api/customrcare/get-errors/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.763060+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/config/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.756371+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/dashboard/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.748286+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/analytics/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.735881+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/errors/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.724033+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/performance/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.712827+00:00", "user": "admin_71f3ea52", "activity_type": "API_CALL", "action": "Viewed /api/log-admin/health/", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T17:38:36.175689+00:00", "user": "testuser2_ffa29745", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T17:38:36.018567+00:00", "user": "testuser_18c7764a", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T16:22:00.484893+00:00", "user": "testuser2_a8e888e1", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T16:22:00.330524+00:00", "user": "testuser_1e65985f", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T16:19:34.400831+00:00", "user": "testuser2", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T16:19:34.245827+00:00", "user": "testuser", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T14:49:28.273577+00:00", "user": "<PERSON><PERSON><PERSON>", "activity_type": "OTHER", "action": "Contributor <PERSON><PERSON>", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T10:28:25.283082+00:00", "user": "nayan", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}, {"timestamp": "2025-06-22T20:46:03.453611+00:00", "user": "nayan", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}, {"timestamp": "2025-06-22T20:42:56.267666+00:00", "user": "testlogin", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}], "security_summary": {"total_incidents": 4, "by_type": [{"incident_type": "BRUTE_FORCE", "count": 4}], "by_severity": [{"severity": "MEDIUM", "count": 4}], "unresolved": 4, "critical_incidents": 0, "blocked_incidents": 0}, "security_details": [{"timestamp": "2025-06-23T17:53:52.024463+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}, {"timestamp": "2025-06-23T17:38:36.024605+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}, {"timestamp": "2025-06-23T16:22:00.335627+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}, {"timestamp": "2025-06-23T16:19:34.251806+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}]}