import time
import json
import traceback
import psutil
import hashlib
from django.utils.deprecation import MiddlewareMixin
from django.db import connection
from django.conf import settings
from django.core.cache import cache
from django.http import JsonResponse
from django.utils import timezone
from .models import (
    PerformanceLog, ErrorLog, UserActivity, APIAccessLog,
    DatabaseQueryLog, AuthenticationLog, SecurityIncident, LogConfig
)
import logging

logger = logging.getLogger(__name__)

class LoggingMiddleware(MiddlewareMixin):
    """Comprehensive logging middleware"""

    def __init__(self, get_response):
        self.get_response = get_response
        self.log_config = self._get_log_config()
        super().__init__(get_response)

    def _get_log_config(self):
        """Get logging configuration with caching"""
        config = cache.get('log_config')
        if not config:
            try:
                config = LogConfig.objects.first()
                if not config:
                    config = LogConfig.objects.create()
                cache.set('log_config', config, timeout=300)  # Cache for 5 minutes
            except Exception:
                # Fallback configuration
                config = type('LogConfig', (), {
                    'enable_performance_logging': True,
                    'enable_error_logging': True,
                    'enable_activity_logging': True,
                    'enable_api_logging': True,
                    'enable_db_logging': False,
                })()
        return config

    def process_request(self, request):
        """Initialize request tracking"""
        request.start_time = time.time()
        request.start_queries = len(connection.queries)

        # Get system metrics
        try:
            process = psutil.Process()
            request.start_memory = process.memory_info().rss / 1024 / 1024  # MB
            request.start_cpu = process.cpu_percent()
        except Exception:
            request.start_memory = 0
            request.start_cpu = 0

        # Log API access if enabled
        if self.log_config.enable_api_logging and request.path.startswith('/api/'):
            self._log_api_access_start(request)

    def process_response(self, request, response):
        """Log performance and API access"""
        if not hasattr(request, 'start_time'):
            return response

        duration = time.time() - request.start_time

        # Performance logging
        if self.log_config.enable_performance_logging:
            self._log_performance(request, response, duration)

        # API access logging
        if self.log_config.enable_api_logging and request.path.startswith('/api/'):
            self._log_api_access_complete(request, response, duration)

        # Database query logging
        if self.log_config.enable_db_logging:
            self._log_database_queries(request, duration)

        return response

    def process_exception(self, request, exception):
        """Log exceptions"""
        if self.log_config.enable_error_logging:
            self._log_error(request, exception)
        return None

    def _log_performance(self, request, response, duration):
        """Log performance metrics"""
        try:
            # Calculate metrics
            memory_usage = 0
            cpu_usage = 0
            try:
                process = psutil.Process()
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_usage = current_memory - getattr(request, 'start_memory', 0)
                cpu_usage = process.cpu_percent() - getattr(request, 'start_cpu', 0)
            except Exception:
                pass

            db_queries_count = len(connection.queries) - getattr(request, 'start_queries', 0)
            db_queries_time = sum(float(q['time']) for q in connection.queries[getattr(request, 'start_queries', 0):])

            response_size = len(response.content) if hasattr(response, 'content') else 0

            PerformanceLog.objects.create(
                path=request.path,
                method=request.method,
                duration=duration,
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                db_queries_count=db_queries_count,
                db_queries_time=db_queries_time,
                user=request.user if request.user.is_authenticated else None,
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                ip_address=self._get_client_ip(request),
                response_size=response_size,
                status_code=response.status_code,
            )
        except Exception as e:
            logger.error(f"Error logging performance: {e}")

    def _log_api_access_start(self, request):
        """Log API access start"""
        request._api_log_data = {
            'endpoint': request.path,
            'method': request.method,
            'user': request.user if request.user.is_authenticated else None,
            'ip_address': self._get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'request_headers': dict(request.headers),
        }

        # Log request body for POST/PUT/PATCH
        if request.method in ['POST', 'PUT', 'PATCH']:
            try:
                if hasattr(request, 'body') and request.body:
                    content_type = request.content_type
                    if 'application/json' in content_type:
                        request._api_log_data['request_body'] = json.loads(request.body.decode('utf-8'))
                    elif 'application/x-www-form-urlencoded' in content_type:
                        request._api_log_data['request_body'] = dict(request.POST)
            except Exception:
                pass

    def _log_api_access_complete(self, request, response, duration):
        """Complete API access logging"""
        try:
            if not hasattr(request, '_api_log_data'):
                return

            log_data = request._api_log_data
            response_size = len(response.content) if hasattr(response, 'content') else 0

            # Check for rate limiting
            rate_limited = response.status_code == 429

            # Check if response was cached
            cached_response = 'X-Cache-Hit' in response.headers if hasattr(response, 'headers') else False

            APIAccessLog.objects.create(
                endpoint=log_data['endpoint'],
                method=log_data['method'],
                user=log_data['user'],
                ip_address=log_data['ip_address'],
                user_agent=log_data['user_agent'],
                request_headers=log_data['request_headers'],
                request_body=log_data.get('request_body', {}),
                response_status=response.status_code,
                response_size=response_size,
                response_time=duration,
                rate_limited=rate_limited,
                cached_response=cached_response,
            )
        except Exception as e:
            logger.error(f"Error logging API access: {e}")

    def _log_database_queries(self, request, duration):
        """Log database queries"""
        try:
            start_queries = getattr(request, 'start_queries', 0)
            queries = connection.queries[start_queries:]

            for query in queries:
                query_time = float(query['time'])
                sql = query['sql']

                # Extract query type and table
                query_type = sql.split()[0].upper()
                table_name = self._extract_table_name(sql)

                # Create query hash
                query_hash = hashlib.md5(sql.encode()).hexdigest()

                DatabaseQueryLog.objects.create(
                    query_type=query_type,
                    table_name=table_name,
                    query_hash=query_hash,
                    execution_time=query_time,
                    user=request.user if request.user.is_authenticated else None,
                    view_name=getattr(request, 'resolver_match', {}).get('view_name', ''),
                    slow_query=query_time > 1.0,  # Mark as slow if > 1 second
                )
        except Exception as e:
            logger.error(f"Error logging database queries: {e}")

    def _log_error(self, request, exception):
        """Log errors and exceptions"""
        try:
            # Determine error type
            error_type = 'UNKNOWN'
            severity = 'MEDIUM'

            if hasattr(exception, '__class__'):
                exception_name = exception.__class__.__name__
                if 'ValidationError' in exception_name:
                    error_type = 'VALIDATION'
                    severity = 'LOW'
                elif 'PermissionDenied' in exception_name:
                    error_type = 'PERMISSION'
                    severity = 'MEDIUM'
                elif 'NotFound' in exception_name or '404' in str(exception):
                    error_type = 'NOT_FOUND'
                    severity = 'LOW'
                elif 'DatabaseError' in exception_name:
                    error_type = 'DATABASE'
                    severity = 'HIGH'
                elif any(x in exception_name for x in ['500', 'Internal', 'Server']):
                    error_type = 'SERVER'
                    severity = 'HIGH'

            # Get request data
            request_data = {}
            try:
                if request.method in ['POST', 'PUT', 'PATCH']:
                    if hasattr(request, 'body') and request.body:
                        content_type = request.content_type
                        if 'application/json' in content_type:
                            request_data = json.loads(request.body.decode('utf-8'))
                        elif 'application/x-www-form-urlencoded' in content_type:
                            request_data = dict(request.POST)
            except Exception:
                pass

            ErrorLog.objects.create(
                view_name=getattr(request, 'resolver_match', {}).get('view_name', ''),
                error_type=error_type,
                severity=severity,
                error_message=str(exception),
                stack_trace=traceback.format_exc(),
                request_data=request_data,
                user=request.user if request.user.is_authenticated else None,
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                path=request.path,
                method=request.method,
            )
        except Exception as e:
            logger.error(f"Error logging exception: {e}")

    def _get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def _extract_table_name(self, sql):
        """Extract table name from SQL query"""
        try:
            sql_upper = sql.upper()
            if 'FROM' in sql_upper:
                parts = sql_upper.split('FROM')[1].split()
                if parts:
                    table_name = parts[0].strip('`"[]')
                    return table_name.split('.')[1] if '.' in table_name else table_name
            elif 'INTO' in sql_upper:
                parts = sql_upper.split('INTO')[1].split()
                if parts:
                    table_name = parts[0].strip('`"[]')
                    return table_name.split('.')[1] if '.' in table_name else table_name
            elif 'UPDATE' in sql_upper:
                parts = sql_upper.split('UPDATE')[1].split()
                if parts:
                    table_name = parts[0].strip('`"[]')
                    return table_name.split('.')[1] if '.' in table_name else table_name
        except Exception:
            pass
        return ''


class ActivityLoggingMiddleware(MiddlewareMixin):
    """Middleware for logging user activities"""

    def __init__(self, get_response):
        self.get_response = get_response
        self.log_config = self._get_log_config()
        super().__init__(get_response)

    def _get_log_config(self):
        """Get logging configuration with caching"""
        config = cache.get('log_config')
        if not config:
            try:
                config = LogConfig.objects.first()
                if not config:
                    config = LogConfig.objects.create()
                cache.set('log_config', config, timeout=300)
            except Exception:
                config = type('LogConfig', (), {'enable_activity_logging': True})()
        return config

    def process_response(self, request, response):
        """Log user activities"""
        if not self.log_config.enable_activity_logging:
            return response

        if not request.user.is_authenticated:
            return response

        # Skip logging for certain paths
        skip_paths = ['/admin/jsi18n/', '/static/', '/media/', '/favicon.ico']
        if any(request.path.startswith(path) for path in skip_paths):
            return response

        try:
            activity_type = self._determine_activity_type(request)
            action = self._generate_action_description(request, response)

            if activity_type and action:
                UserActivity.objects.create(
                    user=request.user,
                    activity_type=activity_type,
                    action=action,
                    description=f"{request.method} {request.path}",
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    session_key=request.session.session_key or '',
                    path=request.path,
                    method=request.method,
                    success=200 <= response.status_code < 400,
                    metadata={
                        'status_code': response.status_code,
                        'response_size': len(response.content) if hasattr(response, 'content') else 0,
                    }
                )
        except Exception as e:
            logger.error(f"Error logging user activity: {e}")

        return response

    def _determine_activity_type(self, request):
        """Determine activity type based on request"""
        path = request.path.lower()
        method = request.method.upper()

        if 'login' in path:
            return 'LOGIN'
        elif 'logout' in path:
            return 'LOGOUT'
        elif 'register' in path or 'signup' in path:
            return 'REGISTER'
        elif 'password' in path:
            return 'PASSWORD_CHANGE'
        elif 'profile' in path and method in ['PUT', 'PATCH']:
            return 'PROFILE_UPDATE'
        elif 'upload' in path and method == 'POST':
            return 'FILE_UPLOAD'
        elif 'download' in path:
            return 'FILE_DOWNLOAD'
        elif 'payment' in path or 'razorpay' in path:
            return 'PAYMENT'
        elif 'subscription' in path or 'package' in path:
            return 'SUBSCRIPTION'
        elif 'question' in path and method == 'POST':
            return 'QUESTION_SUBMIT'
        elif 'test' in path or 'exam' in path:
            return 'TEST_ATTEMPT'
        elif '/admin/' in path:
            return 'ADMIN_ACTION'
        elif '/api/' in path:
            return 'API_CALL'
        else:
            return 'OTHER'

    def _generate_action_description(self, request, response):
        """Generate human-readable action description"""
        method = request.method
        path = request.path
        status = response.status_code

        if method == 'GET':
            return f"Viewed {path}"
        elif method == 'POST':
            return f"Created/Submitted to {path}"
        elif method == 'PUT':
            return f"Updated {path}"
        elif method == 'PATCH':
            return f"Modified {path}"
        elif method == 'DELETE':
            return f"Deleted from {path}"
        else:
            return f"{method} {path}"

    def _get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SecurityMiddleware(MiddlewareMixin):
    """Middleware for security monitoring and incident detection"""

    def __init__(self, get_response):
        self.get_response = get_response
        self.suspicious_patterns = [
            'union select', 'drop table', 'insert into', 'delete from',
            '<script', 'javascript:', 'onload=', 'onerror=',
            '../', '..\\', '/etc/passwd', '/proc/version',
            'cmd.exe', 'powershell', 'bash', 'sh -c'
        ]
        super().__init__(get_response)

    def process_request(self, request):
        """Monitor for security threats"""
        try:
            # Check for suspicious patterns in request
            self._check_sql_injection(request)
            self._check_xss_attempts(request)
            self._check_path_traversal(request)
            self._check_rate_limiting(request)

        except Exception as e:
            logger.error(f"Error in security middleware: {e}")

        return None

    def _check_sql_injection(self, request):
        """Check for SQL injection attempts"""
        suspicious_sql = ['union select', 'drop table', 'insert into', 'delete from',
                         'update set', 'alter table', 'create table', 'exec(', 'execute(']

        request_data = self._get_request_data(request)
        request_str = str(request_data).lower()

        for pattern in suspicious_sql:
            if pattern in request_str:
                self._log_security_incident(
                    request, 'SQL_INJECTION', 'HIGH',
                    f"Potential SQL injection attempt detected: {pattern}",
                    {'pattern': pattern, 'request_data': request_data}
                )
                break

    def _check_xss_attempts(self, request):
        """Check for XSS attempts"""
        xss_patterns = ['<script', 'javascript:', 'onload=', 'onerror=', 'onclick=',
                       'onmouseover=', 'onfocus=', 'onblur=', 'onchange=']

        request_data = self._get_request_data(request)
        request_str = str(request_data).lower()

        for pattern in xss_patterns:
            if pattern in request_str:
                self._log_security_incident(
                    request, 'XSS_ATTEMPT', 'MEDIUM',
                    f"Potential XSS attempt detected: {pattern}",
                    {'pattern': pattern, 'request_data': request_data}
                )
                break

    def _check_path_traversal(self, request):
        """Check for path traversal attempts"""
        traversal_patterns = ['../', '..\\', '/etc/', '/proc/', '/var/', 'c:\\', 'd:\\']

        request_data = self._get_request_data(request)
        request_str = str(request_data).lower()
        path = request.path.lower()

        for pattern in traversal_patterns:
            if pattern in request_str or pattern in path:
                self._log_security_incident(
                    request, 'UNAUTHORIZED_ACCESS', 'HIGH',
                    f"Potential path traversal attempt: {pattern}",
                    {'pattern': pattern, 'path': request.path}
                )
                break

    def _check_rate_limiting(self, request):
        """Check for potential brute force attacks"""
        ip = self._get_client_ip(request)
        cache_key = f"security_requests_{ip}"

        # Get request count for this IP in the last minute
        request_count = cache.get(cache_key, 0)
        request_count += 1
        cache.set(cache_key, request_count, timeout=60)

        # If more than 100 requests per minute, log as suspicious
        if request_count > 100:
            self._log_security_incident(
                request, 'RATE_LIMIT_EXCEEDED', 'MEDIUM',
                f"Excessive requests from IP: {request_count} requests in 1 minute",
                {'request_count': request_count, 'time_window': '1 minute'}
            )

    def _get_request_data(self, request):
        """Get request data for analysis"""
        data = {}
        try:
            if request.method in ['POST', 'PUT', 'PATCH']:
                if hasattr(request, 'body') and request.body:
                    content_type = request.content_type
                    if 'application/json' in content_type:
                        data = json.loads(request.body.decode('utf-8'))
                    elif 'application/x-www-form-urlencoded' in content_type:
                        data = dict(request.POST)

            # Also check GET parameters
            data.update(dict(request.GET))
        except Exception:
            pass

        return data

    def _log_security_incident(self, request, incident_type, severity, description, additional_data):
        """Log security incident"""
        try:
            SecurityIncident.objects.create(
                incident_type=incident_type,
                severity=severity,
                description=description,
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                user=request.user if request.user.is_authenticated else None,
                path=request.path,
                request_data=additional_data,
                blocked=False,  # Could implement blocking logic here
            )
        except Exception as e:
            logger.error(f"Error logging security incident: {e}")

    def _get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


# Keep the original middleware for backward compatibility
class PerformanceMonitorMiddleware(MiddlewareMixin):
    def process_request(self, request):
        request.start_time = time.time()

    def process_response(self, request, response):
        duration = time.time() - getattr(request, 'start_time', time.time())
        PerformanceLog.objects.create(
            path=request.path,
            duration=duration,
            user=request.user if request.user.is_authenticated else None
        )
        return response