from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.apps import apps
import google.generativeai as genai

# Gemini setup
genai.configure(api_key='AIzaSyD2y8WM03f5fBDIo2uPfO-r8FiKkYuO7hQ')
gemini_model = genai.GenerativeModel('gemini-2.0-flash')


def get_all_model_metadata():
    metadata = {}
    for model in apps.get_models():
        model_name = model.__name__
        field_names = [field.name for field in model._meta.fields]
        metadata[model_name] = {
            'model': model,
            'fields': field_names,
            'keywords': [model_name.lower()] + [f.lower() for f in field_names]
        }
    return metadata

def find_best_matching_model(query, metadata):
    scores = []
    query_lower = query.lower()
    for name, meta in metadata.items():
        score = sum(1 for kw in meta['keywords'] if kw in query_lower)
        if score > 0:
            scores.append((score, meta))
    if scores:
        scores.sort(reverse=True)  # higher score = better match
        return scores[0][1]
    return None

class AIChatbotAPIView(APIView):
    def post(self, request):
        query = request.data.get('query', '')
        if not query:
            return Response({'error': 'Query not provided'}, status=status.HTTP_400_BAD_REQUEST)

        model_metadata = get_all_model_metadata()

        # Match query with model keywords
        matched_model = None
        for name, meta in model_metadata.items():
            if any(keyword.lower() in query.lower() for keyword in meta['keywords']):
                matched_model = meta
                break

        if not matched_model:
            return Response({'error': 'No matching model found'}, status=status.HTTP_404_NOT_FOUND)

        # Get model and fetch data
        model_class = matched_model['model']
        fields = matched_model['fields']
        data_qs = model_class.objects.all()[:5]
        data_list = list(data_qs.values(*fields))

        # Get explanation from Gemini
        prompt = f"""
            You are an AI assistant helping explain database records to a student.

            User asked: '{query}'

            Model name: {model_class.__name__}

            Here is the data from the database (JSON format):
            {data_list}

            Please explain what this data represents and highlight the important fields in a simple, student-friendly way.
            """
        gemini_response = gemini_model.generate_content(prompt)
        explanation = gemini_response.text
        return Response({
            'model': model_class.__name__,
            'data': data_list,
            'explanation': explanation,
        })
