from rest_framework import serializers
from contributor.models import Contributor<PERSON><PERSON>file
from contributor.serializers import ContributorProfileSerializer
from questions.serializers import (
    MasterOptionSerializer,
    MasterQuestionSerializer,
    QuestionSerializer,
)
from .models import BlogPost
from bs4 import BeautifulSoup
from django.utils.html import strip_tags
from django.db.models import Q


class BlogPostSerializer(serializers.ModelSerializer):
    seo = serializers.SerializerMethodField()
    normal_questions = QuestionSerializer(many=True, read_only=True, source="questions")
    master_questions = MasterQuestionSerializer(
        many=True, read_only=True, source="master_question"
    )
    master_options = MasterOptionSerializer(
        many=True, read_only=True, source="master_option"
    )
    author_first_name = serializers.CharField(
        source="author.user.first_name", read_only=True
    )
    author_last_name = serializers.CharField(
        source="author.user.last_name", read_only=True
    )

    class Meta:
        model = BlogPost
        fields = [
            "id",
            "slug",
            "title",
            "author",
            "author_first_name",
            "author_last_name",
            "category",
            "introduction",
            "content",
            "short_content",
            "internal_link",
            "external_link",
            "tags",
            "published_date",
            "updated_date",
            "image",
            "image_caption",
            "views_counts",
            "meta_title",
            "meta_description",
            "meta_keywords",
            "canonical_url",
            "open_graph",
            "twitter_cards",
            "breadcrumb_schema",
            "article_schema",
            "seo",
            "approval_status",
            "normal_questions",
            "master_questions",
            "master_options",
            "reason",
            "reason_document"
        ]
        read_only_fields = ["slug", "published_date", "updated_date"]

    def get_seo(self, obj):
        return {
            "meta_title": obj.meta_title,
            "meta_description": obj.meta_description,
            "meta_keywords": obj.meta_keywords,
            "canonical_url": obj.canonical_url,
            "open_graph": obj.open_graph,
            "twitter_cards": obj.twitter_cards,
            "breadcrumb_schema": obj.breadcrumb_schema,
            "article_schema": obj.article_schema,
        }

    def create(self, validated_data):
        # Ensure no required fields are None
        required_fields = ['title', 'author', 'content']  # Add all required fields here
        for field in required_fields:
            if validated_data.get(field) is None:
                raise ValueError(f"{field} cannot be None")
        

        instance = BlogPost.objects.create(**validated_data)
        return instance

    def update(self, instance, validated_data):
        instance.title = validated_data.get("title", instance.title)
        instance.author = validated_data.get("author", instance.author)
        instance.introduction = validated_data.get(
            "introduction", instance.introduction
        )
        instance.content = validated_data.get("content", instance.content)
        instance.short_content = validated_data.get(
            "short_content", instance.short_content
        )
        instance.internal_link = validated_data.get(
            "internal_link", instance.internal_link
        )
        instance.external_link = validated_data.get(
            "external_link", instance.external_link
        )
        instance.image = validated_data.get("image", instance.image)
        instance.image_caption = validated_data.get(
            "image_caption", instance.image_caption
        )
        instance.meta_title = validated_data.get("meta_title", instance.meta_title)
        instance.meta_description = validated_data.get(
            "meta_description", instance.meta_description
        )
        instance.meta_keywords = validated_data.get(
            "meta_keywords", instance.meta_keywords
        )
        instance.canonical_url = validated_data.get(
            "canonical_url", instance.canonical_url
        )
        instance.open_graph = validated_data.get("open_graph", instance.open_graph)
        instance.twitter_cards = validated_data.get(
            "twitter_cards", instance.twitter_cards
        )
        instance.breadcrumb_schema = validated_data.get(
            "breadcrumb_schema", instance.breadcrumb_schema
        )
        instance.article_schema = validated_data.get(
            "article_schema", instance.article_schema
        )
        if "reason_document" in validated_data:
            instance.reason_document = validated_data.get("reason_document", instance.reason_document)

        instance.save()

        # Insert internal links after updating content
        instance.content = self.insert_internal_links(instance)
        instance.save()
        return instance

    def insert_internal_links(self, post):
        """
        Automatically adds internal links to the blog post content.
        """
        content = post.content
        keywords = self.extract_keywords(
            content
        )  # Function to identify relevant keywords
        blacklist = post.blacklist_keywords or []
        related_articles = (
            BlogPost.objects.filter(
                Q(tags__icontains=post.tags) | Q(category=post.category),
                published_date__lt=post.published_date,
            )
            .exclude(id=post.id)
            .distinct()[:10]
        )  
        if not related_articles:
            return content 
        breakpoint()

        soup = BeautifulSoup(content, "html.parser")
        for keyword in keywords:
            if keyword in blacklist:
                continue  # Skip blacklisted keywords
            for article in related_articles:
                if keyword in strip_tags(str(soup)):
                    link = f'<a href="{article.get_absolute_url()}" title="{article.meta_description}" style="color: maroon; font-weight: bold; text-decoration: underline;">{keyword}</a>'
                    content = content.replace(keyword, link, 1)
                    break  


        related_section = "<h3>Related Articles</h3><ul>"
        for article in related_articles[
            :5
        ]:  
            related_section += f'<li><a href="{article.get_absolute_url()}" title="{article.meta_description}" style="color: maroon; font-weight: bold; text-decoration: underline;">{article.title}</a></li>'
        related_section += "</ul>"
        content += related_section

        return str(soup)

    def extract_keywords(self, content):
        """
        Extracts keywords from the blog post content.
        """
        words = strip_tags(content).split()
        keywords = [
            word for word in words if len(word) > 4
        ]  
        return list(set(keywords))  

class BlogPostForPublicSerializer(serializers.ModelSerializer):
    seo = serializers.SerializerMethodField()
    
    author_first_name = serializers.CharField(
        source="author.user.first_name", read_only=True
    )
    author_last_name = serializers.CharField(
        source="author.user.last_name", read_only=True
    )

    class Meta:
        model = BlogPost
        fields = [
            "id",
            "slug",
            "title",
            "author",
            "author_first_name",
            "author_last_name",
            "category",
            "introduction",
            "content",
            "short_content",
            "internal_link",
            "external_link",
            "tags",
            "published_date",
            "updated_date",
            "image",
            "image_caption",
            "views_counts",
            "meta_title",
            "meta_description",
            "meta_keywords",
            "canonical_url",
            "open_graph",
            "twitter_cards",
            "breadcrumb_schema",
            "article_schema",
            "seo",
            "approval_status",
          
        ]
        read_only_fields = ["slug", "published_date", "updated_date"]

    def get_seo(self, obj):
        return {
            "meta_title": obj.meta_title,
            "meta_description": obj.meta_description,
            "meta_keywords": obj.meta_keywords,
            "canonical_url": obj.canonical_url,
            "open_graph": obj.open_graph,
            "twitter_cards": obj.twitter_cards,
            "breadcrumb_schema": obj.breadcrumb_schema,
            "article_schema": obj.article_schema,
        }
