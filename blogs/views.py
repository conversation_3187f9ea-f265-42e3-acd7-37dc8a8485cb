from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import Blog<PERSON>ost
from .serializers import Blog<PERSON>ostSerializer, BlogPostForPublicSerializer
from contributor.permissions import IsContributorUser
from .permissions import IsBlogPostUser

class BlogPostList(APIView):
    permission_classes = (IsBlogPostUser,)
    def get(self, request):
        blogposts = BlogPost.objects.all().order_by('-created_at')
        serializer = BlogPostSerializer(blogposts, many=True)
        return Response(serializer.data)

    def post(self, request):
        serializer = BlogPostSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

from django.shortcuts import get_object_or_404
from rest_framework.permissions import IsAuthenticated  
class BlogPostDetail(APIView):
    permission_classes = (IsContributorUser,)

    def get_object(self, slug):
        return get_object_or_404(BlogPost, slug=slug)

    def get(self, request, slug):
        blogpost = self.get_object(slug)
        blogpost.views_counts += 1
        blogpost.save(update_fields=["views_counts"])
        serializer = BlogPostSerializer(blogpost)
        return Response(serializer.data)

    def put(self, request, slug):
        blogpost = self.get_object(slug)
        serializer = BlogPostSerializer(blogpost, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def patch(self, request, slug):
        blogpost = self.get_object(slug)
        serializer = BlogPostSerializer(blogpost, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        blogpost = self.get_object(slug)
        blogpost.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class BlogPostListForPublic(APIView):
    def get(self, request):
        blogposts = BlogPost.objects.filter(approval_status="approved").order_by('-created_at')
        serializer = BlogPostForPublicSerializer(blogposts, many=True)
        return Response(serializer.data)
    

class BlogPostForPublicDetail(APIView):
    def get(self, request, slug):
        blogpost = get_object_or_404(BlogPost, slug=slug, approval_status="approved")
        blogpost.views_counts += 1
        blogpost.save(update_fields=["views_counts"])
        serializer = BlogPostForPublicSerializer(blogpost)
        return Response(serializer.data)


class BlogViewCounter(APIView):
    def get(self, request, blog_id):
        blog = get_object_or_404(BlogPost, id=blog_id)
        blog.views += 1
        blog.save()
        return Response({
            "id": blog.id,
            "title": blog.title,
            "views": blog.views
        }, status=status.HTTP_200_OK)

