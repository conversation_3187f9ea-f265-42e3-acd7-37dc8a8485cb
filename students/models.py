from django.db import models
from django.contrib.auth.models import User
import random
import string
from django.utils import timezone
from django.core.files.base import ContentFile
import qrcode
from io import BytesIO
from django.utils.timezone import now, timedelta
# from customrcare.models import ExamGroupLevel

class Student(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="student_profile"
    )
    student_id = models.CharField(max_length=25, unique=True, null=True, blank=True)
    phone = models.CharField(max_length=15, unique=True)
    course = models.CharField(max_length=100, null=True, blank=True)
    role = models.CharField(max_length=30, default="student")
    account_type = models.CharField(
        max_length=20, default="student", null=True, blank=True
    )
    account_status = models.CharField(
        max_length=20, default="active", null=True, blank=True
    )
    subscription_type = models.Char<PERSON>ield(
        max_length=20, default="trial", null=True, blank=True
    )
    active_services = models.TextField(null=True, blank=True)
    address = models.TextField(null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    gender = models.CharField(max_length=10, null=True, blank=True)
    language_preferred = models.CharField(
        max_length=20, default="English", null=True, blank=True
    )
    referral_code = models.CharField(max_length=10, blank=True, null=True)
    qr_code = models.ImageField(upload_to="qr_codes/", blank=True, null=True)
    total_points = models.IntegerField(default=0)
    refferred_count = models.PositiveIntegerField(default=0)
    image_url = models.CharField(max_length=500, null=True, blank=True)
    last_login_date = models.DateField(null=True, blank=True)
    streak  = models.IntegerField(default=0)
    streak_reason = models.TextField(null=True, blank=True)
    exam_group = models.ForeignKey("customrcare.ExamGroupLevel", on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.username

    def save(self, *args, **kwargs):
        if not self.student_id:
            self.student_id = f"stu_{self.user.username}_{self.phone[-3:]}"
        super().save(*args, **kwargs)

    @property
    def current_subscription_status(self):
        """Get current subscription status based on active subscriptions"""
        from packages_and_subscriptions.models import Subscription
        from django.utils import timezone

        active_subscriptions = Subscription.objects.filter(
            student=self,
            is_active=True,
            end_date__gte=timezone.now()
        ).order_by('-start_date')

        if not active_subscriptions.exists():
            return "Free"

        # Get the latest active subscription
        latest_subscription = active_subscriptions.first()

        # Check if it's a valid subscription (considering event packages)
        if latest_subscription.is_valid_subscription():
            return latest_subscription.package.name
        else:
            return "Expired"

    @property
    def active_subscriptions(self):
        """Get all currently active subscriptions"""
        from packages_and_subscriptions.models import Subscription
        from django.utils import timezone

        return Subscription.objects.filter(
            student=self,
            is_active=True,
            end_date__gte=timezone.now()
        ).order_by('-start_date')

    @property
    def validity_subscriptions(self):
        """Get active validity-based subscriptions"""
        return self.active_subscriptions.filter(package__package_type='validity')

    @property
    def event_subscriptions(self):
        """Get active event-based subscriptions"""
        return self.active_subscriptions.filter(package__package_type='event')

    def has_access_to_content(self, content_id=None):
        """Check if student has access to specific content"""
        active_subs = self.active_subscriptions

        for subscription in active_subs:
            if subscription.can_access_content(content_id):
                return True

        return False

    def update_subscription_type_field(self):
        """Update the subscription_type field based on current subscriptions"""
        self.subscription_type = self.current_subscription_status
        self.save(update_fields=['subscription_type'])

    def generate_qr_code(self):
        # Generating QR code image for the referral link
        referral_url = f"http://example.com/refer/{self.referral_code}"  # Your base URL for referral redirect
        qr_img = qrcode.make(referral_url)

        # Saving the QR code as an image file in the model's qr_code field
        buffer = BytesIO()
        qr_img.save(buffer, format="PNG")
        buffer.seek(0)

        self.qr_code.save(
            f"{self.referral_code}.png", ContentFile(buffer.read()), save=False
        )

    def update_streak(self):
        today = now().date()
        if self.last_login_date == today:
            return  # Already logged in today

        if self.last_login_date == today - timedelta(days=1):
            self.streats += 1  # Continue streak
        else:
            self.streats = 1  # Reset streak

        self.last_login_date = today
        self.save()


class Referral(models.Model):
    referrer = models.ForeignKey(
        Student, on_delete=models.CASCADE, related_name="referrals"
    )
    referred = models.ForeignKey(
        Student,
        on_delete=models.CASCADE,
        related_name="referred",
        null=True,
        blank=True,
    )
    referral_code = models.CharField(max_length=10)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.referral_code:
            # Generate a unique referral code
            self.referral_code = self.generate_referral_code()
        super().save(*args, **kwargs)

    def generate_referral_code(self):
        return "".join(random.choices(string.ascii_uppercase + string.digits, k=8))

    def __str__(self):
        return self.referral_code


class ReferralPoints(models.Model):
    student = models.OneToOneField(
        Student, on_delete=models.CASCADE, related_name="points"
    )
    points = models.IntegerField(default=0)

    def __str__(self):
        return f"{self.student.user.username} has {self.points} points"


class WithdrawalRequest(models.Model):
    student = models.ForeignKey(
        Student, on_delete=models.CASCADE, related_name="withdrawal_requests"
    )
    points = models.PositiveIntegerField()
    upi_id = models.CharField(max_length=50)
    transaction_id = models.CharField(max_length=20, unique=True, editable=False)
    status = models.CharField(max_length=20, default="pending")
    notes = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    def save(self, *args, **kwargs):
        if not self.transaction_id:
            self.transaction_id = self.generate_transaction_id()
        super().save(*args, **kwargs)

    def generate_transaction_id(self):
        today = timezone.now().strftime("%Y%m%d")
        prefix = f"WD-{today}-"
        last_request = (
            WithdrawalRequest.objects.filter(transaction_id__startswith=prefix)
            .order_by("id")
            .last()
        )

        if last_request:
            last_id = int(last_request.transaction_id[-3:])
            new_id = f"{last_id + 1:03}"
        else:
            new_id = "001"

        return f"{prefix}{new_id}"

    def __str__(self):
        return f"Request {self.transaction_id} by {self.student.user.username}"

class SignupContent(models.Model):
    heading = models.CharField(max_length=255, null=True, blank=True)
    subtext_1 = models.TextField(null=True, blank=True)
    subtext_2 = models.TextField(null=True, blank=True)
    urls = models.JSONField(default=list,null=True, blank=True)  # Store URLs as a JSON list

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.heading


class ScratchCard(models.Model):
    referrer = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='scratch_cards')
    referral = models.ForeignKey(Referral, on_delete=models.CASCADE, related_name='scratch_cards')
    amount = models.IntegerField(default=0)
    is_scratched = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    scratched_at = models.DateTimeField(null=True, blank=True)
    WithdrawalRequest = models.ForeignKey(
        WithdrawalRequest, on_delete=models.CASCADE, null=True, blank=True, related_name='scratch_cards'
    )

    def __str__(self):
        return f"ScratchCard - {self.amount} for {self.referrer.user.username}"


