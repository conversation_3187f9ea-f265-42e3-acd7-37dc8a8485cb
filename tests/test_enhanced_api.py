#!/usr/bin/env python3
"""
Test the enhanced phone validation through the API
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_enhanced_validation_api():
    """Test the enhanced validation through the API"""
    
    # Use the existing token
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUwODgwODcwLCJpYXQiOjE3NTA4NjI4NzAsImp0aSI6IjY3YzU4NDJkOWZiODQ3MjBiMWFhZjk4OGRkY2ZlNjViIiwidXNlcl9pZCI6MTQ3fQ.Xvl--cF1oEDnKagXatO4_i6Ej6wwRp5iHjVKhrV1Mkw"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    # Test data with various validation scenarios
    test_data = {
        "contacts": [
            {"name": "Valid Number", "contact": "9876543210"},
            {"name": "Invalid Short", "contact": "12345"},
            {"name": "USSD Balance", "contact": "*121#"},
            {"name": "Invalid Prefix", "contact": "1234567890"},
            {"name": "IMEI Check", "contact": "*#06#"},
            {"name": "Too Short Special", "contact": "*#"},
            {"name": "Valid with Formatting", "contact": "+91-9876-543-211"},
            {"name": "Invalid Long", "contact": "98765432101234"},
        ]
    }
    
    print("🧪 Testing Enhanced Phone Validation via API")
    print("=" * 60)
    print(f"Sending {len(test_data['contacts'])} contacts for validation...")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/contacts/sync/",
            headers=headers,
            json=test_data,
            timeout=30
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print("✅ API Response:")
            print(f"  Success: {data['success']}")
            print(f"  Message: {data['message']}")
            print(f"  Created: {data['data']['created']}")
            print(f"  Updated: {data['data']['updated']}")
            print(f"  Matched: {data['data']['matched']}")
            print(f"  Total Processed: {data['data']['total_processed']}")
            print(f"  Errors: {len(data['data']['errors'])}")
            
            if data['data']['errors']:
                print("\n📋 Validation Errors (Expected):")
                for error in data['data']['errors']:
                    print(f"  - {error}")
            
            print(f"\n📊 Summary:")
            print(f"  - Valid contacts processed: {data['data']['total_processed']}")
            print(f"  - Invalid contacts rejected: {len(data['data']['errors'])}")
            print(f"  - Total contacts sent: {len(test_data['contacts'])}")
            
        else:
            print(f"❌ API Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error text: {response.text}")
        
        return response.status_code == 201
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {str(e)}")
        return False

def main():
    print("🚀 Enhanced Phone Validation API Test")
    print("=" * 80)
    
    success = test_enhanced_validation_api()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 Enhanced validation is working correctly via API!")
        print("✅ Features verified:")
        print("  • Filters out numbers with less than 10 digits")
        print("  • Supports special numbers with # and * characters")
        print("  • Validates Indian mobile number prefixes")
        print("  • Provides detailed error messages")
    else:
        print("❌ API test failed!")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
