#!/usr/bin/env python3
"""
Fix Django settings.py to include contacts app
"""

import os
import re
import shutil
from datetime import datetime

def backup_settings():
    """Create a backup of settings.py"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"shashtrarth/settings.py.backup.{timestamp}"
    shutil.copy2("shashtrarth/settings.py", backup_path)
    print(f"✅ Created backup: {backup_path}")
    return backup_path

def check_contacts_in_settings():
    """Check if contacts is already in INSTALLED_APPS"""
    with open('shashtrarth/settings.py', 'r') as f:
        content = f.read()
    return '"contacts"' in content or "'contacts'" in content

def add_contacts_to_installed_apps():
    """Add contacts to INSTALLED_APPS"""
    with open('shashtrarth/settings.py', 'r') as f:
        content = f.read()
    
    # Pattern to find INSTALLED_APPS and add contacts after log_admin
    pattern = r'(INSTALLED_APPS\s*=\s*\[.*?"log_admin",?\s*\n)'
    replacement = r'\1    "contacts",\n'
    
    new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    
    # If the above didn't work, try a different approach
    if new_content == content:
        # Find the closing bracket of INSTALLED_APPS and add contacts before it
        pattern = r'(INSTALLED_APPS\s*=\s*\[.*?)(^\])'
        replacement = r'\1    "contacts",\n\2'
        new_content = re.sub(pattern, replacement, content, flags=re.DOTALL | re.MULTILINE)
    
    if new_content != content:
        with open('shashtrarth/settings.py', 'w') as f:
            f.write(new_content)
        print("✅ Added contacts to INSTALLED_APPS")
        return True
    else:
        print("❌ Failed to add contacts to INSTALLED_APPS")
        return False

def show_installed_apps():
    """Display current INSTALLED_APPS"""
    with open('shashtrarth/settings.py', 'r') as f:
        content = f.read()
    
    # Extract INSTALLED_APPS section
    match = re.search(r'INSTALLED_APPS\s*=\s*\[(.*?)\]', content, re.DOTALL)
    if match:
        apps_content = match.group(1)
        print("📋 Current INSTALLED_APPS:")
        for line in apps_content.strip().split('\n'):
            if line.strip():
                print(f"  {line.strip()}")
    else:
        print("❌ Could not find INSTALLED_APPS in settings.py")

def main():
    print("🚀 Starting Django settings.py fix for contacts app...")
    
    # Change to project directory
    if not os.path.exists('shashtrarth/settings.py'):
        print("❌ settings.py not found. Make sure you're in the project root directory.")
        return False
    
    # Show current state
    print("\n📋 Before changes:")
    show_installed_apps()
    
    # Check if contacts is already there
    if check_contacts_in_settings():
        print("\n✅ contacts app is already in INSTALLED_APPS")
        return True
    
    # Create backup
    backup_path = backup_settings()
    
    # Add contacts to INSTALLED_APPS
    print("\n🔧 Adding contacts to INSTALLED_APPS...")
    success = add_contacts_to_installed_apps()
    
    if success:
        print("\n📋 After changes:")
        show_installed_apps()
        print("\n✅ Successfully updated settings.py")
        return True
    else:
        # Restore backup
        shutil.copy2(backup_path, "shashtrarth/settings.py")
        print(f"\n❌ Failed to update settings.py. Restored from backup: {backup_path}")
        return False

if __name__ == "__main__":
    main()
