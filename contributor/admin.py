from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Sum
from .models import ContributorProfile, ContributorPoints, Banner, PopupBanner, ContributorEarning
from .earning_service import EarningCalculationService

# Register your models here.


@admin.register(PopupBanner)
class PopupBannerAdmin(admin.ModelAdmin):
    """
    Admin interface for PopupBanner with comprehensive management features
    """
    list_display = [
        'title', 'content_type', 'approval_status', 'is_active',
        'priority', 'created_by', 'created_at', 'image_preview'
    ]
    list_filter = [
        'approval_status', 'content_type', 'priority', 'is_active',
        'created_at', 'updated_at'
    ]
    search_fields = [
        'title', 'description', 'text_content', 'link_text',
        'created_by__username', 'created_by__email'
    ]
    readonly_fields = [
        'slug', 'created_at', 'updated_at', 'approved_at', 'activated_at',
        'image_preview', 'approval_workflow_status'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'content_type', 'slug')
        }),
        ('Content', {
            'fields': ('image', 'image_preview', 'text_content', 'link_url', 'link_text', 'anchor_tag'),
            'classes': ('collapse',)
        }),
        ('Display Settings', {
            'fields': ('display_duration', 'priority', 'is_active')
        }),
        ('Approval Workflow', {
            'fields': (
                'approval_status', 'approval_workflow_status', 'rejection_reason',
                'created_by', 'approved_by_care', 'approved_by_admin', 'rejected_by'
            ),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'approved_at', 'activated_at'),
            'classes': ('collapse',)
        }),
    )

    # Ordering
    ordering = ['-created_at']

    # Items per page
    list_per_page = 25

    # Actions
    actions = ['approve_by_admin', 'reject_banners', 'activate_banners', 'deactivate_banners']

    def image_preview(self, obj):
        """Display image preview in admin"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = "Image Preview"

    def approval_workflow_status(self, obj):
        """Display approval workflow status with colors"""
        status_colors = {
            'pending': '#ffc107',
            'approved_by_care': '#17a2b8',
            'approved_by_admin': '#28a745',
            'rejected_by_care': '#dc3545',
            'rejected_by_admin': '#dc3545',
            'active': '#28a745',
            'inactive': '#6c757d',
        }
        color = status_colors.get(obj.approval_status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_approval_status_display()
        )
    approval_workflow_status.short_description = "Workflow Status"

    def approve_by_admin(self, request, queryset):
        """Bulk action to approve banners by admin"""
        count = 0
        for banner in queryset:
            if banner.approve_by_admin(request.user):
                banner.save()
                count += 1

        self.message_user(
            request,
            f'{count} banner(s) were successfully approved by admin.'
        )
    approve_by_admin.short_description = "Approve selected banners (Admin)"

    def reject_banners(self, request, queryset):
        """Bulk action to reject banners"""
        count = 0
        for banner in queryset:
            if banner.reject(request.user, "Bulk rejection by admin"):
                banner.save()
                count += 1

        self.message_user(
            request,
            f'{count} banner(s) were successfully rejected.'
        )
    reject_banners.short_description = "Reject selected banners"

    def activate_banners(self, request, queryset):
        """Bulk action to activate banners"""
        count = 0
        for banner in queryset:
            if banner.activate():
                banner.save()
                count += 1

        self.message_user(
            request,
            f'{count} banner(s) were successfully activated.'
        )
    activate_banners.short_description = "Activate selected banners"

    def deactivate_banners(self, request, queryset):
        """Bulk action to deactivate banners"""
        count = 0
        for banner in queryset:
            if banner.deactivate():
                banner.save()
                count += 1

        self.message_user(
            request,
            f'{count} banner(s) were successfully deactivated.'
        )
    deactivate_banners.short_description = "Deactivate selected banners"

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related(
            'created_by', 'approved_by_care__user', 'approved_by_admin', 'rejected_by'
        )


@admin.register(ContributorProfile)
class ContributorProfileAdmin(admin.ModelAdmin):
    """Enhanced admin interface for ContributorProfile with earning management"""

    list_display = [
        'user', 'role', 'custom_points_display', 'total_earnings_display',
        'current_month_points', 'slug'
    ]
    list_filter = ['role', 'custom_points', 'user__date_joined']
    search_fields = ['user__username', 'user__email', 'user__first_name', 'user__last_name']
    readonly_fields = ['slug', 'total_earnings_display', 'current_month_points', 'earning_summary']

    fieldsets = (
        ('User Information', {
            'fields': ('user', 'role', 'slug')
        }),
        ('Security', {
            'fields': ('security_question', 'security_answer'),
            'classes': ('collapse',)
        }),
        ('Points Configuration', {
            'fields': ('custom_points',),
            'description': 'Set custom point values for this contributor. If not set, default points will be used.'
        }),
        ('Earnings Summary', {
            'fields': ('total_earnings_display', 'current_month_points', 'earning_summary'),
            'classes': ('collapse',)
        }),
    )

    actions = ['calculate_current_month_earnings', 'reset_custom_points']

    def custom_points_display(self, obj):
        """Display custom points configuration"""
        if obj.custom_points:
            return format_html(
                '<span style="color: #28a745; font-weight: bold;">{}</span>',
                obj.custom_points.name
            )
        return format_html(
            '<span style="color: #6c757d;">Default</span>'
        )
    custom_points_display.short_description = "Points Config"

    def total_earnings_display(self, obj):
        """Display total lifetime earnings"""
        try:
            total_data = EarningCalculationService.get_contributor_total_earnings(obj)
            return format_html(
                '<strong>{}</strong> points<br/><small>{} records</small>',
                total_data['total_points'],
                total_data['earning_records_count']
            )
        except Exception as e:
            return format_html('<span style="color: red;">Error: {}</span>', str(e))
    total_earnings_display.short_description = "Total Earnings"

    def current_month_points(self, obj):
        """Display current month points"""
        try:
            summary = EarningCalculationService.get_earnings_summary(obj, 'monthly')
            return format_html(
                '<strong>{}</strong> points',
                summary['total_points']
            )
        except Exception as e:
            return format_html('<span style="color: red;">Error: {}</span>', str(e))
    current_month_points.short_description = "Current Month"

    def earning_summary(self, obj):
        """Display detailed earning summary"""
        try:
            summary = EarningCalculationService.get_earnings_summary(obj, 'monthly')
            breakdown = summary['activity_breakdown']

            html = "<table style='width: 100%; border-collapse: collapse;'>"
            html += "<tr style='background: #f8f9fa;'><th>Activity</th><th>Count</th><th>Points</th></tr>"

            for activity, data in breakdown.items():
                activity_name = activity.replace('_', ' ').title()
                html += f"<tr><td>{activity_name}</td><td>{data['count']}</td><td>{data['points']}</td></tr>"

            html += f"<tr style='background: #e9ecef; font-weight: bold;'><td>Total</td><td>-</td><td>{summary['total_points']}</td></tr>"
            html += "</table>"

            return format_html(html)
        except Exception as e:
            return format_html('<span style="color: red;">Error: {}</span>', str(e))
    earning_summary.short_description = "Current Month Breakdown"

    def calculate_current_month_earnings(self, request, queryset):
        """Bulk action to calculate current month earnings"""
        count = 0
        for contributor in queryset:
            try:
                EarningCalculationService.calculate_and_update_earnings(contributor, 'monthly')
                count += 1
            except Exception as e:
                self.message_user(
                    request,
                    f'Error calculating earnings for {contributor.user.username}: {e}',
                    level='ERROR'
                )

        self.message_user(
            request,
            f'Successfully calculated earnings for {count} contributor(s).'
        )
    calculate_current_month_earnings.short_description = "Calculate current month earnings"

    def reset_custom_points(self, request, queryset):
        """Bulk action to reset custom points to default"""
        count = queryset.update(custom_points=None)
        self.message_user(
            request,
            f'Reset custom points for {count} contributor(s) to default.'
        )
    reset_custom_points.short_description = "Reset to default points"


@admin.register(ContributorPoints)
class ContributorPointsAdmin(admin.ModelAdmin):
    """Enhanced admin interface for ContributorPoints"""

    list_display = [
        'name', 'normal_questions', 'master_questions', 'master_options',
        'blogs', 'previous_questions', 'assigned_contributors_count', 'last_updated'
    ]
    list_filter = ['last_updated', 'created_at']
    search_fields = ['name', 'slug']
    readonly_fields = ['slug', 'assigned_contributors_count', 'assigned_contributors_list']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug')
        }),
        ('Point Values', {
            'fields': (
                'normal_questions', 'master_questions', 'master_options',
                'blogs', 'previous_questions'
            ),
            'description': 'Set point values for different contributor activities.'
        }),
        ('Usage Information', {
            'fields': ('assigned_contributors_count', 'assigned_contributors_list'),
            'classes': ('collapse',)
        }),
    )

    def assigned_contributors_count(self, obj):
        """Count of contributors using this points configuration"""
        return obj.assigned_contributors.count()
    assigned_contributors_count.short_description = "Assigned Contributors"

    def assigned_contributors_list(self, obj):
        """List of contributors using this points configuration"""
        contributors = obj.assigned_contributors.all()[:10]  # Limit to first 10
        if contributors:
            names = [c.user.username for c in contributors]
            if obj.assigned_contributors.count() > 10:
                names.append(f"... and {obj.assigned_contributors.count() - 10} more")
            return ", ".join(names)
        return "None"
    assigned_contributors_list.short_description = "Assigned To"


@admin.register(ContributorEarning)
class ContributorEarningAdmin(admin.ModelAdmin):
    """Admin interface for ContributorEarning records"""

    list_display = [
        'contributor', 'period_type', 'period_start', 'total_points',
        'total_earnings', 'is_paid', 'created_at'
    ]
    list_filter = [
        'period_type', 'is_paid', 'period_start', 'created_at',
        'contributor__role'
    ]
    search_fields = [
        'contributor__user__username', 'contributor__user__email'
    ]
    readonly_fields = [
        'total_points', 'created_at', 'updated_at', 'points_breakdown'
    ]
    date_hierarchy = 'period_start'

    fieldsets = (
        ('Basic Information', {
            'fields': ('contributor', 'period_type', 'period_start', 'period_end')
        }),
        ('Activity Counts', {
            'fields': (
                'normal_questions_count', 'master_questions_count', 'master_options_count',
                'blogs_count', 'previous_questions_count'
            ),
            'classes': ('collapse',)
        }),
        ('Points Earned', {
            'fields': (
                'normal_questions_points', 'master_questions_points', 'master_options_points',
                'blogs_points', 'previous_questions_points', 'total_points', 'points_breakdown'
            )
        }),
        ('Payment Information', {
            'fields': ('total_earnings', 'is_paid', 'paid_at'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('points_config_used', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_paid', 'mark_as_unpaid', 'recalculate_earnings']

    def points_breakdown(self, obj):
        """Display points breakdown in a table"""
        html = "<table style='width: 100%; border-collapse: collapse;'>"
        html += "<tr style='background: #f8f9fa;'><th>Activity</th><th>Count</th><th>Points</th></tr>"

        activities = [
            ('Normal Questions', obj.normal_questions_count, obj.normal_questions_points),
            ('Master Questions', obj.master_questions_count, obj.master_questions_points),
            ('Master Options', obj.master_options_count, obj.master_options_points),
            ('Blogs', obj.blogs_count, obj.blogs_points),
            ('Previous Questions', obj.previous_questions_count, obj.previous_questions_points),
        ]

        for name, count, points in activities:
            html += f"<tr><td>{name}</td><td>{count}</td><td>{points}</td></tr>"

        html += f"<tr style='background: #e9ecef; font-weight: bold;'><td>Total</td><td>-</td><td>{obj.total_points}</td></tr>"
        html += "</table>"

        return format_html(html)
    points_breakdown.short_description = "Points Breakdown"

    def mark_as_paid(self, request, queryset):
        """Mark selected earnings as paid"""
        from django.utils import timezone
        count = queryset.update(is_paid=True, paid_at=timezone.now())
        self.message_user(request, f'Marked {count} earning record(s) as paid.')
    mark_as_paid.short_description = "Mark as paid"

    def mark_as_unpaid(self, request, queryset):
        """Mark selected earnings as unpaid"""
        count = queryset.update(is_paid=False, paid_at=None)
        self.message_user(request, f'Marked {count} earning record(s) as unpaid.')
    mark_as_unpaid.short_description = "Mark as unpaid"

    def recalculate_earnings(self, request, queryset):
        """Recalculate selected earnings"""
        count = 0
        for earning in queryset:
            try:
                EarningCalculationService.calculate_and_update_earnings(
                    earning.contributor,
                    earning.period_type,
                    earning.period_start
                )
                count += 1
            except Exception as e:
                self.message_user(
                    request,
                    f'Error recalculating {earning}: {e}',
                    level='ERROR'
                )

        self.message_user(request, f'Recalculated {count} earning record(s).')
    recalculate_earnings.short_description = "Recalculate earnings"


admin.site.register(Banner)
